import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

Rectangle {
    id: root
    
    property var device
    
    height: contentLayout.implicitHeight + 20
    color: "#f5f5f5"
    border.color: "#ddd"
    border.width: 1
    radius: 8
    
    // Hover effect
    MouseArea {
        anchors.fill: parent
        hoverEnabled: true
        onEntered: parent.color = "#e8e8e8"
        onExited: parent.color = "#f5f5f5"
    }
    
    ColumnLayout {
        id: contentLayout
        anchors.fill: parent
        anchors.margins: 15
        spacing: 8
        
        // Header with device name and status
        RowLayout {
            Layout.fillWidth: true
            
            Label {
                text: device ? (device.product || "Unknown Device") : "Unknown Device"
                font.bold: true
                font.pixelSize: 16
                color: "#333"
                Layout.fillWidth: true
            }
            
            Rectangle {
                width: statusLabel.implicitWidth + 16
                height: statusLabel.implicitHeight + 8
                color: device && device.status === "Connected" ? "#4CAF50" : "#FF5722"
                radius: 12
                
                Label {
                    id: statusLabel
                    anchors.centerIn: parent
                    text: device ? device.status : "Unknown"
                    color: "white"
                    font.pixelSize: 12
                    font.bold: true
                }
            }
        }
        
        // Device details grid
        GridLayout {
            Layout.fillWidth: true
            columns: 4
            columnSpacing: 20
            rowSpacing: 8
            
            // VID
            Label {
                text: qsTr("VID:")
                font.bold: true
                color: "#666"
            }
            Label {
                text: device ? device.vid : "N/A"
                font.family: "Consolas, Monaco, monospace"
                color: "#2196F3"
                font.bold: true
            }
            
            // PID
            Label {
                text: qsTr("PID:")
                font.bold: true
                color: "#666"
            }
            Label {
                text: device ? device.pid : "N/A"
                font.family: "Consolas, Monaco, monospace"
                color: "#2196F3"
                font.bold: true
            }
            
            // Manufacturer
            Label {
                text: qsTr("Manufacturer:")
                font.bold: true
                color: "#666"
            }
            Label {
                text: device ? (device.manufacturer || "Unknown") : "Unknown"
                Layout.columnSpan: 3
                Layout.fillWidth: true
                wrapMode: Text.WordWrap
            }
            
            // Device Class
            Label {
                text: qsTr("Class:")
                font.bold: true
                color: "#666"
            }
            Label {
                text: device ? device.deviceClass : "Unknown"
                Layout.columnSpan: 3
                Layout.fillWidth: true
                color: "#FF9800"
                font.bold: true
            }
            
            // Device Path
            Label {
                text: qsTr("Path:")
                font.bold: true
                color: "#666"
            }
            Label {
                text: device ? device.devicePath : "Unknown"
                Layout.columnSpan: 3
                Layout.fillWidth: true
                font.family: "Consolas, Monaco, monospace"
                font.pixelSize: 11
                color: "#666"
                wrapMode: Text.WordWrap
            }
        }
    }
    
    // Subtle shadow effect
    Rectangle {
        anchors.fill: parent
        anchors.topMargin: 2
        anchors.leftMargin: 2
        color: "transparent"
        border.color: "#00000020"
        border.width: 1
        radius: parent.radius
        z: -1
    }
}
