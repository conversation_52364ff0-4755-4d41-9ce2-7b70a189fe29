QT += core quick

CONFIG += c++17

# You can make your code fail to compile if it uses deprecated APIs.
# In order to do so, uncomment the following line.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

TARGET = UsbDetect
TEMPLATE = app

SOURCES += \
    main.cpp \
    usbmonitor.cpp \
    usbdevice.cpp

HEADERS += \
    usbmonitor.h \
    usbdevice.h

RESOURCES += qml.qrc

# Additional import path used to resolve QML modules in Qt Creator's code model
QML_IMPORT_PATH =

# Additional import path used to resolve QML modules just for Qt Quick Designer
QML_DESIGNER_IMPORT_PATH =

# Platform-specific libraries and configurations
win32 {
    LIBS += -lsetupapi
    DEFINES += _WIN32
}

unix:!macx {
    # Linux specific configuration
    CONFIG += link_pkgconfig
    PKGCONFIG += libudev
    DEFINES += __linux__
}

# Default rules for deployment.
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target
