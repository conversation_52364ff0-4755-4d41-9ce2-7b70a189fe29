#!/bin/bash

echo "Building USB Device Monitor with qmake..."

# Check if qmake is available
if ! command -v qmake &> /dev/null; then
    echo "Error: qmake not found in PATH!"
    echo "Please make sure Qt is properly installed and qmake is in your PATH."
    echo "You can also open UsbDetect.pro directly in Qt Creator."
    exit 1
fi

# Check if libudev is installed
if ! pkg-config --exists libudev; then
    echo "Error: libudev development library is not installed."
    echo "Please install it using:"
    echo "  Ubuntu/Debian: sudo apt-get install libudev-dev"
    echo "  CentOS/RHEL/Fedora: sudo yum install libudev-devel"
    exit 1
fi

# Clean previous build
echo "Cleaning previous build..."
make clean 2>/dev/null || true
rm -f Makefile *.o moc_*.cpp qrc_*.cpp UsbDetect

# Generate Makefile
echo "Generating Makefile..."
qmake UsbDetect.pro

# Check if qmake was successful
if [ $? -ne 0 ]; then
    echo "qmake failed!"
    exit 1
fi

# Build the project
echo "Building project..."
make

# Check if build was successful
if [ $? -ne 0 ]; then
    echo "Build failed!"
    echo "Please check your Qt installation and compiler setup."
    exit 1
fi

echo "Build completed successfully!"
echo "You can run the executable: ./UsbDetect"
