// 简化版本的USB监控实现，用于测试和备用
#include "usbmonitor.h"
#include <QDebug>
#include <QRegularExpression>

#ifdef _WIN32
#include <windows.h>
#include <setupapi.h>
#include <devguid.h>

// 简化的Windows USB设备扫描实现
void UsbMonitor::scanWindowsUsbDevicesSimple()
{
    qDebug() << "Using simplified Windows USB detection...";
    
    // 扫描所有设备类别
    HDEVINFO deviceInfoSet = SetupDiGetClassDevs(nullptr, nullptr, nullptr, 
                                                  DIGCF_PRESENT | DIGCF_ALLCLASSES);
    
    if (deviceInfoSet == INVALID_HANDLE_VALUE) {
        qWarning() << "Failed to get device info set";
        return;
    }

    SP_DEVINFO_DATA deviceInfoData;
    deviceInfoData.cbSize = sizeof(SP_DEVINFO_DATA);
    
    for (DWORD i = 0; SetupDiEnumDeviceInfo(deviceInfoSet, i, &deviceInfoData); i++) {
        // 获取硬件ID
        DWORD dataType;
        DWORD requiredSize = 0;
        
        // 获取硬件ID的大小
        SetupDiGetDeviceRegistryProperty(deviceInfoSet, &deviceInfoData, SPDRP_HARDWAREID,
                                       &dataType, nullptr, 0, &requiredSize);
        
        if (requiredSize == 0) continue;
        
        // 获取硬件ID
        QByteArray buffer(requiredSize, 0);
        if (!SetupDiGetDeviceRegistryProperty(deviceInfoSet, &deviceInfoData, SPDRP_HARDWAREID,
                                            &dataType, (PBYTE)buffer.data(), requiredSize, nullptr)) {
            continue;
        }
        
        QString hardwareId = QString::fromWCharArray((wchar_t*)buffer.data());
        
        // 只处理USB设备
        if (!hardwareId.contains("USB\\VID_", Qt::CaseInsensitive)) {
            continue;
        }
        
        // 提取VID和PID
        QRegularExpression vidPidRegex(R"(USB\\VID_([0-9A-Fa-f]{4})&PID_([0-9A-Fa-f]{4}))");
        QRegularExpressionMatch match = vidPidRegex.match(hardwareId);
        
        if (!match.hasMatch()) {
            continue;
        }
        
        QString vid = match.captured(1).toUpper();
        QString pid = match.captured(2).toUpper();
        
        // 获取设备描述
        QString deviceDesc = "USB Device";
        requiredSize = 0;
        SetupDiGetDeviceRegistryProperty(deviceInfoSet, &deviceInfoData, SPDRP_DEVICEDESC,
                                       &dataType, nullptr, 0, &requiredSize);
        if (requiredSize > 0) {
            QByteArray descBuffer(requiredSize, 0);
            if (SetupDiGetDeviceRegistryProperty(deviceInfoSet, &deviceInfoData, SPDRP_DEVICEDESC,
                                               &dataType, (PBYTE)descBuffer.data(), requiredSize, nullptr)) {
                deviceDesc = QString::fromWCharArray((wchar_t*)descBuffer.data());
            }
        }
        
        // 获取制造商
        QString manufacturer = "Unknown";
        requiredSize = 0;
        SetupDiGetDeviceRegistryProperty(deviceInfoSet, &deviceInfoData, SPDRP_MFG,
                                       &dataType, nullptr, 0, &requiredSize);
        if (requiredSize > 0) {
            QByteArray mfgBuffer(requiredSize, 0);
            if (SetupDiGetDeviceRegistryProperty(deviceInfoSet, &deviceInfoData, SPDRP_MFG,
                                               &dataType, (PBYTE)mfgBuffer.data(), requiredSize, nullptr)) {
                manufacturer = QString::fromWCharArray((wchar_t*)mfgBuffer.data());
            }
        }
        
        // 简单的设备类别判断
        QString deviceClass = "USB Device";
        if (hardwareId.contains("HID", Qt::CaseInsensitive)) {
            deviceClass = "HID (Human Interface Device)";
        } else if (hardwareId.contains("STORAGE", Qt::CaseInsensitive)) {
            deviceClass = "Mass Storage";
        } else if (hardwareId.contains("AUDIO", Qt::CaseInsensitive)) {
            deviceClass = "Audio";
        }
        
        // 创建设备对象
        UsbDevice *device = new UsbDevice(vid, pid, hardwareId, manufacturer, 
                                        deviceDesc, deviceClass, "Connected", this);
        m_devices.append(device);
        
        qDebug() << "Found USB device:" << vid << pid << deviceDesc;
    }
    
    SetupDiDestroyDeviceInfoList(deviceInfoSet);
    qDebug() << "Found" << m_devices.count() << "USB devices";
}
#endif
