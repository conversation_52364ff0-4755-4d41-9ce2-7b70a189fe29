@echo off
echo Building USB Device Monitor with qmake...

REM Check if qmake is available
qmake --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Error: qmake not found in PATH!
    echo Please make sure Qt is properly installed and qmake is in your PATH.
    echo You can also open UsbDetect.pro directly in Qt Creator.
    pause
    exit /b 1
)

REM Clean previous build
if exist Makefile del Makefile
if exist *.o del *.o
if exist moc_*.cpp del moc_*.cpp
if exist qrc_*.cpp del qrc_*.cpp
if exist UsbDetect.exe del UsbDetect.exe

REM Generate Makefile
echo Generating Makefile...
qmake UsbDetect.pro

REM Check if qmake was successful
if %ERRORLEVEL% neq 0 (
    echo qmake failed!
    pause
    exit /b 1
)

REM Build the project
echo Building project...
nmake

REM Check if build was successful
if %ERRORLEVEL% neq 0 (
    echo Build failed! Trying with mingw32-make...
    mingw32-make
    if %ERRORLEVEL% neq 0 (
        echo Build failed with both nmake and mingw32-make!
        echo Please check your Qt installation and compiler setup.
        pause
        exit /b 1
    )
)

echo Build completed successfully!
echo You can run the executable: UsbDetect.exe
pause
