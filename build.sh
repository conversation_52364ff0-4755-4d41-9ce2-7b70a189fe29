#!/bin/bash

echo "Building USB Device Monitor..."

# Check if libudev is installed
if ! pkg-config --exists libudev; then
    echo "Error: libudev development library is not installed."
    echo "Please install it using:"
    echo "  Ubuntu/Debian: sudo apt-get install libudev-dev"
    echo "  CentOS/RHEL/Fedora: sudo yum install libudev-devel"
    exit 1
fi

# Create build directory if it doesn't exist
if [ ! -d "build" ]; then
    mkdir build
fi

# Change to build directory
cd build

# Configure with CMake
echo "Configuring project with CMake..."
cmake ..

# Check if configuration was successful
if [ $? -ne 0 ]; then
    echo "CMake configuration failed!"
    echo "Please make sure Qt 6.8 is installed and CMAKE_PREFIX_PATH is set correctly."
    echo "Example: export CMAKE_PREFIX_PATH=/opt/Qt/6.8.0/gcc_64"
    exit 1
fi

# Build the project
echo "Building project..."
cmake --build . --config Release

# Check if build was successful
if [ $? -ne 0 ]; then
    echo "Build failed!"
    exit 1
fi

echo "Build completed successfully!"
echo "You can find the executable in the build directory."
