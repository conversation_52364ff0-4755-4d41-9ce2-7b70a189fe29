[{"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-D_WIN32", "-DWINVER=0x0600", "-D_WIN32_WINNT=0x0600", "-DQT_QML_DEBUG", "-DQT_QUICK_LIB", "-DQT_OPENGL_LIB", "-DQT_GUI_LIB", "-DQT_QMLMETA_LIB", "-DQT_QMLMODELS_LIB", "-DQT_QMLWORKERSCRIPT_LIB", "-DQT_QML_LIB", "-DQT_QMLINTEGRATION_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\app\\QT\\UsbDetect", "-IC:\\Qt\\6.9.0\\mingw_64\\include", "-IC:\\Qt\\6.9.0\\mingw_64\\include\\QtQuick", "-IC:\\Qt\\6.9.0\\mingw_64\\include\\QtOpenGL", "-IC:\\Qt\\6.9.0\\mingw_64\\include\\QtGui", "-IC:\\Qt\\6.9.0\\mingw_64\\include\\QtQmlMeta", "-IC:\\Qt\\6.9.0\\mingw_64\\include\\QtQmlModels", "-IC:\\Qt\\6.9.0\\mingw_64\\include\\QtQmlWorkerScript", "-IC:\\Qt\\6.9.0\\mingw_64\\include\\QtQml", "-IC:\\Qt\\6.9.0\\mingw_64\\include\\QtQmlIntegration", "-IC:\\Qt\\6.9.0\\mingw_64\\include\\QtNetwork", "-IC:\\Qt\\6.9.0\\mingw_64\\include\\QtCore", "-ID:\\app\\QT\\UsbDetect\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\debug", "-ID:\\app\\QT\\UsbDetect\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug", "-IC:\\Qt\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\app\\QT\\UsbDetect\\main.cpp"], "directory": "D:/app/QT/UsbDetect/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/app/QT/UsbDetect/main.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-D_WIN32", "-DWINVER=0x0600", "-D_WIN32_WINNT=0x0600", "-DQT_QML_DEBUG", "-DQT_QUICK_LIB", "-DQT_OPENGL_LIB", "-DQT_GUI_LIB", "-DQT_QMLMETA_LIB", "-DQT_QMLMODELS_LIB", "-DQT_QMLWORKERSCRIPT_LIB", "-DQT_QML_LIB", "-DQT_QMLINTEGRATION_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\app\\QT\\UsbDetect", "-IC:\\Qt\\6.9.0\\mingw_64\\include", "-IC:\\Qt\\6.9.0\\mingw_64\\include\\QtQuick", "-IC:\\Qt\\6.9.0\\mingw_64\\include\\QtOpenGL", "-IC:\\Qt\\6.9.0\\mingw_64\\include\\QtGui", "-IC:\\Qt\\6.9.0\\mingw_64\\include\\QtQmlMeta", "-IC:\\Qt\\6.9.0\\mingw_64\\include\\QtQmlModels", "-IC:\\Qt\\6.9.0\\mingw_64\\include\\QtQmlWorkerScript", "-IC:\\Qt\\6.9.0\\mingw_64\\include\\QtQml", "-IC:\\Qt\\6.9.0\\mingw_64\\include\\QtQmlIntegration", "-IC:\\Qt\\6.9.0\\mingw_64\\include\\QtNetwork", "-IC:\\Qt\\6.9.0\\mingw_64\\include\\QtCore", "-ID:\\app\\QT\\UsbDetect\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\debug", "-ID:\\app\\QT\\UsbDetect\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug", "-IC:\\Qt\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\app\\QT\\UsbDetect\\usbmonitor.cpp"], "directory": "D:/app/QT/UsbDetect/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/app/QT/UsbDetect/usbmonitor.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-D_WIN32", "-DWINVER=0x0600", "-D_WIN32_WINNT=0x0600", "-DQT_QML_DEBUG", "-DQT_QUICK_LIB", "-DQT_OPENGL_LIB", "-DQT_GUI_LIB", "-DQT_QMLMETA_LIB", "-DQT_QMLMODELS_LIB", "-DQT_QMLWORKERSCRIPT_LIB", "-DQT_QML_LIB", "-DQT_QMLINTEGRATION_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\app\\QT\\UsbDetect", "-IC:\\Qt\\6.9.0\\mingw_64\\include", "-IC:\\Qt\\6.9.0\\mingw_64\\include\\QtQuick", "-IC:\\Qt\\6.9.0\\mingw_64\\include\\QtOpenGL", "-IC:\\Qt\\6.9.0\\mingw_64\\include\\QtGui", "-IC:\\Qt\\6.9.0\\mingw_64\\include\\QtQmlMeta", "-IC:\\Qt\\6.9.0\\mingw_64\\include\\QtQmlModels", "-IC:\\Qt\\6.9.0\\mingw_64\\include\\QtQmlWorkerScript", "-IC:\\Qt\\6.9.0\\mingw_64\\include\\QtQml", "-IC:\\Qt\\6.9.0\\mingw_64\\include\\QtQmlIntegration", "-IC:\\Qt\\6.9.0\\mingw_64\\include\\QtNetwork", "-IC:\\Qt\\6.9.0\\mingw_64\\include\\QtCore", "-ID:\\app\\QT\\UsbDetect\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\debug", "-ID:\\app\\QT\\UsbDetect\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug", "-IC:\\Qt\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "D:\\app\\QT\\UsbDetect\\usbdevice.cpp"], "directory": "D:/app/QT/UsbDetect/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/app/QT/UsbDetect/usbdevice.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-D_WIN32", "-DWINVER=0x0600", "-D_WIN32_WINNT=0x0600", "-DQT_QML_DEBUG", "-DQT_QUICK_LIB", "-DQT_OPENGL_LIB", "-DQT_GUI_LIB", "-DQT_QMLMETA_LIB", "-DQT_QMLMODELS_LIB", "-DQT_QMLWORKERSCRIPT_LIB", "-DQT_QML_LIB", "-DQT_QMLINTEGRATION_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\app\\QT\\UsbDetect", "-IC:\\Qt\\6.9.0\\mingw_64\\include", "-IC:\\Qt\\6.9.0\\mingw_64\\include\\QtQuick", "-IC:\\Qt\\6.9.0\\mingw_64\\include\\QtOpenGL", "-IC:\\Qt\\6.9.0\\mingw_64\\include\\QtGui", "-IC:\\Qt\\6.9.0\\mingw_64\\include\\QtQmlMeta", "-IC:\\Qt\\6.9.0\\mingw_64\\include\\QtQmlModels", "-IC:\\Qt\\6.9.0\\mingw_64\\include\\QtQmlWorkerScript", "-IC:\\Qt\\6.9.0\\mingw_64\\include\\QtQml", "-IC:\\Qt\\6.9.0\\mingw_64\\include\\QtQmlIntegration", "-IC:\\Qt\\6.9.0\\mingw_64\\include\\QtNetwork", "-IC:\\Qt\\6.9.0\\mingw_64\\include\\QtCore", "-ID:\\app\\QT\\UsbDetect\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\debug", "-ID:\\app\\QT\\UsbDetect\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug", "-IC:\\Qt\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\app\\QT\\UsbDetect\\usbmonitor.h"], "directory": "D:/app/QT/UsbDetect/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/app/QT/UsbDetect/usbmonitor.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-std=gnu++1z", "-Wall", "-Wextra", "-Wextra", "-fexceptions", "-mthreads", "-fsyntax-only", "-m64", "--target=x86_64-w64-mingw32", "-DUNICODE", "-D_UNICODE", "-DWIN32", "-DMINGW_HAS_SECURE_API", "-D_WIN32", "-DWINVER=0x0600", "-D_WIN32_WINNT=0x0600", "-DQT_QML_DEBUG", "-DQT_QUICK_LIB", "-DQT_OPENGL_LIB", "-DQT_GUI_LIB", "-DQT_QMLMETA_LIB", "-DQT_QMLMODELS_LIB", "-DQT_QMLWORKERSCRIPT_LIB", "-DQT_QML_LIB", "-DQT_QMLINTEGRATION_LIB", "-DQT_NETWORK_LIB", "-DQT_CORE_LIB", "-DQT_NEEDS_QMAIN", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedMingwHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\app\\QT\\UsbDetect", "-IC:\\Qt\\6.9.0\\mingw_64\\include", "-IC:\\Qt\\6.9.0\\mingw_64\\include\\QtQuick", "-IC:\\Qt\\6.9.0\\mingw_64\\include\\QtOpenGL", "-IC:\\Qt\\6.9.0\\mingw_64\\include\\QtGui", "-IC:\\Qt\\6.9.0\\mingw_64\\include\\QtQmlMeta", "-IC:\\Qt\\6.9.0\\mingw_64\\include\\QtQmlModels", "-IC:\\Qt\\6.9.0\\mingw_64\\include\\QtQmlWorkerScript", "-IC:\\Qt\\6.9.0\\mingw_64\\include\\QtQml", "-IC:\\Qt\\6.9.0\\mingw_64\\include\\QtQmlIntegration", "-IC:\\Qt\\6.9.0\\mingw_64\\include\\QtNetwork", "-IC:\\Qt\\6.9.0\\mingw_64\\include\\QtCore", "-ID:\\app\\QT\\UsbDetect\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug\\debug", "-ID:\\app\\QT\\UsbDetect\\build\\Desktop_Qt_6_9_0_MinGW_64_bit-Debug", "-IC:\\Qt\\6.9.0\\mingw_64\\mkspecs\\win32-g++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\x86_64-w64-mingw32", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\lib\\gcc\\x86_64-w64-mingw32\\13.1.0\\include\\c++\\backward", "-isystem", "C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\19\\include", "-isystem", "C:\\Qt\\Tools\\mingw1310_64\\x86_64-w64-mingw32\\include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "D:\\app\\QT\\UsbDetect\\usbdevice.h"], "directory": "D:/app/QT/UsbDetect/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/.qtc_clangd", "file": "D:/app/QT/UsbDetect/usbdevice.h"}]