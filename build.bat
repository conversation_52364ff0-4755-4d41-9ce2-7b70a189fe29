@echo off
echo Building USB Device Monitor...

REM Check if build directory exists, create if not
if not exist build mkdir build

REM Change to build directory
cd build

REM Configure with CMake
echo Configuring project with CMake...
cmake ..

REM Check if configuration was successful
if %ERRORLEVEL% neq 0 (
    echo CMake configuration failed!
    echo Please make sure Qt 6.8 is installed and CMAKE_PREFIX_PATH is set correctly.
    echo Example: set CMAKE_PREFIX_PATH=C:\Qt\6.8.0\msvc2022_64
    pause
    exit /b 1
)

REM Build the project
echo Building project...
cmake --build . --config Release

REM Check if build was successful
if %ERRORLEVEL% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo Build completed successfully!
echo You can find the executable in the build\Release directory.
pause
