# Windows 故障排除指南

## 常见编译错误及解决方案

### 1. GUID_DEVINTERFACE_USB_DEVICE 未声明错误

**错误信息**:
```
error: 'GUID_DEVINTERFACE_USB_DEVICE' was not declared in this scope
```

**原因**: 某些Windows SDK版本或MinGW环境中缺少这个定义。

**解决方案**: 
✅ **已修复** - 项目现在使用 `GUID_DEVCLASS_USB` 和备用检测方法，具有更好的兼容性。

### 2. setupapi 库链接错误

**错误信息**:
```
cannot find -lsetupapi
```

**解决方案**:
1. **使用MSVC编译器**（推荐）:
   - 在Qt Creator中选择MSVC Kit
   - 确保安装了Visual Studio或Build Tools

2. **MinGW用户**:
   - 确保使用完整的MinGW-w64发行版
   - 或切换到MSVC编译器

### 3. Unicode 相关错误

**错误信息**:
```
error: cannot convert 'const char*' to 'LPCWSTR'
```

**解决方案**: 
✅ **已修复** - 项目配置已添加Unicode支持定义。

### 4. Windows SDK 版本问题

**错误信息**:
```
error: 'SetupDiGetClassDevs' was not declared
```

**解决方案**:
1. 安装Windows 10/11 SDK
2. 确保SDK版本兼容
3. 项目已设置最低Windows版本为Vista (0x0600)

## 推荐的开发环境配置

### Qt Creator + MSVC (推荐)

1. **安装组件**:
   - Qt 6.8+ with MSVC compiler
   - Visual Studio 2019/2022 或 Build Tools
   - Windows 10/11 SDK

2. **Qt Creator配置**:
   - Tools → Options → Kits
   - 确保选择MSVC编译器
   - 确保Qt版本正确

### Qt Creator + MinGW (备选)

1. **安装组件**:
   - Qt 6.8+ with MinGW compiler
   - 完整的MinGW-w64发行版

2. **注意事项**:
   - 某些Windows API可能不完全支持
   - 建议优先使用MSVC

## 测试USB检测功能

### 方法1: 运行测试程序
```cmd
test_compile.bat
```

### 方法2: 手动编译测试
```cmd
# 使用g++
g++ -std=c++17 test_windows_usb.cpp -lsetupapi -o test_windows_usb.exe

# 使用MSVC
cl /EHsc test_windows_usb.cpp setupapi.lib
```

### 方法3: 在Qt Creator中调试
1. 在 `scanWindowsUsbDevices()` 函数设置断点
2. 运行调试模式
3. 检查设备检测过程

## 运行时问题

### 1. 程序启动但不显示设备

**可能原因**:
- USB设备检测API失败
- 权限问题
- 设备驱动问题

**调试步骤**:
1. 检查Qt Creator的应用程序输出窗口
2. 查看调试信息
3. 运行测试程序验证API功能

### 2. 设备信息显示不完整

**可能原因**:
- 设备驱动未正确安装
- 设备信息注册表项缺失

**解决方案**:
- 更新设备驱动
- 检查设备管理器中的设备状态

### 3. 程序崩溃

**调试步骤**:
1. 在Qt Creator中运行调试版本
2. 检查崩溃位置
3. 查看调用堆栈
4. 检查内存访问错误

## 性能优化建议

### 1. 减少扫描频率
在 `usbmonitor.cpp` 中修改定时器间隔:
```cpp
m_timer->start(5000); // 改为5秒扫描一次
```

### 2. 异步扫描
考虑将USB设备扫描移到后台线程，避免界面卡顿。

### 3. 缓存设备信息
避免重复获取相同设备的信息。

## 获取帮助

### 1. 查看日志输出
在Qt Creator的"应用程序输出"窗口查看调试信息。

### 2. 启用详细日志
在代码中添加更多 `qDebug()` 输出来跟踪问题。

### 3. 系统信息收集
- Windows版本
- Qt版本
- 编译器版本
- 错误信息的完整文本

### 4. 常用调试命令
```cmd
# 查看系统USB设备
devmgmt.msc

# 查看设备详细信息
pnputil /enum-devices /connected

# 查看USB设备树
usbview.exe (Windows SDK工具)
```

## 已知限制

1. **MinGW兼容性**: 某些Windows API在MinGW中支持有限
2. **权限要求**: 某些设备信息可能需要管理员权限
3. **驱动依赖**: 设备信息的完整性取决于驱动程序质量
4. **系统版本**: 最低支持Windows Vista

## 更新日志

- **v1.1**: 修复GUID_DEVINTERFACE_USB_DEVICE未定义错误
- **v1.1**: 添加备用USB检测方法
- **v1.1**: 改进Unicode支持
- **v1.1**: 增强错误处理和日志输出
