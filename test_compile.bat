@echo off
echo Compiling Windows USB test...

REM Try different compilers
echo Trying g++...
g++ -std=c++17 test_windows_usb.cpp -lsetupapi -o test_windows_usb.exe
if %ERRORLEVEL% equ 0 (
    echo Compilation successful with g++!
    echo Running test...
    test_windows_usb.exe
    goto :end
)

echo g++ failed, trying cl (MSVC)...
cl /EHsc test_windows_usb.cpp setupapi.lib
if %ERRORLEVEL% equ 0 (
    echo Compilation successful with cl!
    echo Running test...
    test_windows_usb.exe
    goto :end
)

echo Both compilers failed. Please check your development environment.

:end
pause
