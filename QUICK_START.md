# 快速开始指南

## 🚀 最快上手方式

### 使用Qt Creator（推荐）

1. **打开项目**
   - 启动Qt Creator
   - 选择 `File` -> `Open File or Project...`
   - 选择 `UsbDetect.pro` 文件

2. **配置项目**
   - 选择合适的构建套件（Qt 6.8+）
   - 点击 `Configure Project`

3. **运行程序**
   - 按 `Ctrl+R` 或点击运行按钮
   - 程序将自动编译并启动

### 使用命令行

#### Windows
```cmd
# 使用qmake
build_qmake.bat

# 或使用CMake
build.bat
```

#### Linux
```bash
# 使用qmake
./build_qmake.sh

# 或使用CMake
./build.sh
```

## 📋 系统要求检查

### 必需组件
- ✅ Qt 6.8 或更高版本
- ✅ C++17 编译器
- ✅ CMake 3.16+ （如果使用CMake）

### 平台特定要求

#### Windows
- ✅ Windows SDK（包含setupapi.lib）
- ✅ MSVC 2019/2022 或 MinGW

#### Linux
- ✅ libudev开发库
```bash
# Ubuntu/Debian
sudo apt-get install libudev-dev

# CentOS/RHEL/Fedora
sudo yum install libudev-devel
```

## 🔧 故障排除

### 常见问题

#### Qt Creator找不到Kit
**解决方案**: 
1. 打开 `Tools` -> `Options` -> `Kits`
2. 添加或配置Qt版本和编译器

#### Windows上找不到setupapi
**解决方案**: 
1. 安装Windows SDK
2. 使用MSVC编译器

#### Linux上找不到libudev
**解决方案**: 
```bash
sudo apt-get install libudev-dev  # Ubuntu/Debian
sudo yum install libudev-devel    # CentOS/RHEL
```

#### QML文件加载失败
**解决方案**: 
1. 确保qml.qrc文件存在
2. 重新构建项目

## 📱 使用说明

1. **启动监控**
   - 程序启动后自动开始监控
   - 或点击"Start Monitoring"按钮

2. **查看设备信息**
   - VID/PID：设备的厂商和产品标识
   - 制造商：设备厂商名称
   - 产品：设备产品名称
   - 类别：设备功能分类
   - 状态：连接状态

3. **操作按钮**
   - `Start/Stop Monitoring`：开始/停止监控
   - `Refresh`：手动刷新设备列表

## 📁 项目文件说明

| 文件 | 用途 |
|------|------|
| `UsbDetect.pro` | Qt项目文件，用于Qt Creator |
| `CMakeLists.txt` | CMake构建配置 |
| `main.cpp` | 程序入口点 |
| `usbmonitor.h/.cpp` | USB监控核心类 |
| `usbdevice.h/.cpp` | USB设备数据模型 |
| `main.qml` | 主界面 |
| `UsbDeviceDelegate.qml` | 设备信息显示组件 |
| `qml.qrc` | QML资源文件 |

## 🎯 下一步

- 查看 `README.md` 了解详细信息
- 查看 `QTCREATOR_GUIDE.md` 了解Qt Creator使用技巧
- 查看 `FEATURES.md` 了解功能特性
- 查看 `PROJECT_SUMMARY.md` 了解技术架构

## 💡 开发建议

- 使用Qt Creator的调试功能来跟踪USB设备检测
- 修改 `usbmonitor.cpp` 中的扫描间隔来调整检测频率
- 在 `UsbDeviceDelegate.qml` 中自定义设备信息显示样式
