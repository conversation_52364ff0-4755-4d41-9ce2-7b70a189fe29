/****************************************************************************
** Resource object code
**
** Created by: The Resource Compiler for Qt version 6.9.0
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#ifdef _MSC_VER
// disable informational message "function ... selected for automatic inline expansion"
#pragma warning (disable: 4711)
#endif

static const unsigned char qt_resource_data[] = {
  // UsbDeviceDelegate.qml
  0x0,0x0,0x3,0x7f,
  0x0,
  0x0,0xf,0xfa,0x78,0xda,0xd5,0x57,0x6d,0x4f,0xdb,0x30,0x10,0xfe,0xce,0xaf,0xb0,
  0x82,0x84,0x98,0xc6,0x42,0x5f,0x68,0x57,0x22,0xa1,0x9,0x15,0x3a,0x90,0xd6,0x9,
  0x28,0x1b,0x9f,0xdd,0xc4,0x69,0x2c,0x52,0x3b,0x73,0x9c,0x16,0x18,0xfd,0xef,0xbb,
  0x38,0x89,0x9b,0x34,0x69,0xd5,0xb0,0x9,0x75,0x8e,0xd4,0x26,0xbe,0xb3,0x7d,0xf7,
  0xdc,0x3d,0x67,0x9b,0x4e,0x3,0x2e,0x24,0xba,0x95,0xb7,0x11,0xb5,0x1f,0x51,0xcb,
  0x6c,0x76,0xf6,0x68,0xa1,0xcf,0xec,0x73,0x26,0x5,0xf7,0xc3,0x4a,0xe1,0x37,0xfc,
  0xcc,0x23,0x19,0xa2,0x66,0x2c,0xdb,0xbb,0x23,0xb6,0xc4,0x6c,0xe2,0x13,0xf4,0x7b,
  0xf,0x41,0xa3,0x8e,0x85,0x4,0xe7,0x52,0x7d,0xa8,0x9f,0x40,0xf0,0x80,0x8,0xf9,
  0x8c,0x66,0x58,0x20,0x87,0xcc,0xa8,0x4d,0x96,0x42,0x8f,0xd0,0x89,0x27,0x2d,0x64,
  0xc3,0x8a,0x84,0xc9,0x64,0x6e,0x13,0x56,0xf4,0xa9,0x4d,0xe5,0x95,0x92,0xa2,0x8f,
  0xa8,0xd5,0x50,0xda,0x36,0xf7,0xb9,0xb0,0x90,0xb1,0xef,0x76,0xe2,0xc7,0x50,0x9d,
  0x63,0x2e,0x1c,0x22,0x4c,0x2d,0x73,0x1c,0xa7,0x20,0x98,0x53,0x47,0x7a,0x16,0x6a,
  0xaa,0x3e,0x81,0x1d,0x1a,0x85,0x16,0xea,0x2d,0x4d,0x38,0x3e,0x46,0x57,0x7c,0x46,
  0x4,0x22,0xae,0xb,0xce,0xa8,0xbe,0x21,0x8f,0x42,0x72,0x2e,0x8,0x4e,0xbd,0x8a,
  0x1b,0x66,0xb6,0xc7,0x45,0x68,0xba,0xd4,0xf7,0x2d,0x14,0x60,0x1,0xf6,0x6a,0xa1,
  0x17,0xcf,0x70,0xc9,0xf0,0xd8,0x27,0x0,0x80,0x14,0x11,0xd1,0x22,0xce,0x2e,0xc1,
  0x37,0x11,0xf7,0x27,0x83,0x12,0x53,0xd1,0x19,0xd8,0x4a,0x7a,0xf1,0x63,0xe4,0x75,
  0x9f,0xa8,0xac,0x54,0xcd,0xbb,0xbc,0x58,0x5a,0xdf,0xe7,0x7e,0x34,0x65,0x9,0x6e,
  0x39,0x63,0xe3,0x30,0x14,0x30,0xdd,0xce,0x8d,0x4c,0x38,0xc5,0x62,0x42,0x19,0xe0,
  0x4,0x21,0xce,0x64,0x61,0x80,0x6d,0xca,0x26,0x19,0x76,0xda,0x82,0xc,0x43,0x82,
  0x1,0x6d,0x34,0xa7,0xd2,0x4b,0xa3,0x8c,0x18,0x9e,0x12,0x98,0xd2,0x41,0xa1,0xc4,
  0x32,0xa,0xb5,0xf6,0x1d,0x9f,0x97,0xc,0x8e,0x5b,0x1a,0xfd,0xd8,0xb2,0x87,0x24,
  0x68,0x5,0x20,0xb,0x2b,0x26,0xfa,0x63,0xe2,0xaf,0xcc,0x11,0x37,0x49,0x9e,0x20,
  0xa5,0x52,0x23,0xbe,0xa0,0xc3,0xe4,0xcd,0x84,0x44,0x74,0x22,0x5b,0xa2,0xd7,0x57,
  0x64,0xfc,0x60,0x8f,0x8c,0xcf,0x19,0xba,0x50,0x22,0xe3,0x3,0xb2,0x4a,0x7d,0xa5,
  0x69,0x5d,0xc0,0xd3,0x1c,0x73,0xdf,0xa9,0xb0,0x4b,0xcb,0x3,0xfa,0x44,0xfc,0x11,
  0x7d,0x21,0x80,0x5d,0xb7,0xa4,0xa2,0x93,0xb4,0xdd,0x6e,0x97,0x17,0xd8,0xc2,0xff,
  0xc5,0x7a,0x34,0x56,0x99,0x98,0x6f,0x29,0x7,0x92,0x38,0x28,0xd8,0x34,0xc7,0xd4,
  0x4a,0x40,0xb1,0xa,0x6b,0x33,0x72,0x56,0xd,0xd3,0xd4,0xec,0xad,0x73,0x32,0xc5,
  0xff,0xe0,0x20,0x7d,0x33,0x93,0x69,0xd0,0xd9,0x19,0xa4,0x33,0x54,0x18,0x6,0xf6,
  0x12,0xc7,0x80,0x0,0x19,0xfb,0x27,0xfd,0xf3,0x41,0xa7,0x61,0xc4,0x51,0xd8,0x1f,
  0xc,0x3a,0x9f,0x5b,0xad,0x32,0x3a,0x19,0x75,0x9b,0xad,0x92,0xa8,0x2,0xc9,0xea,
  0xcc,0xc8,0xa8,0x91,0xf3,0xa8,0x52,0x27,0xe3,0x81,0x4d,0x62,0xea,0x5e,0xb3,0x12,
  0x51,0x36,0xa4,0x5b,0xd1,0xdb,0x65,0x5e,0x19,0x95,0x83,0xb3,0x8c,0x98,0x7b,0xc0,
  0xfb,0x6a,0x95,0x52,0x5e,0xb5,0xd6,0xab,0x6d,0x48,0xcf,0xc5,0x9a,0x44,0x5a,0x54,
  0xd2,0x39,0x21,0x1,0x38,0x23,0x31,0x85,0x8d,0x60,0x22,0xa8,0xa3,0xc5,0x5f,0xe1,
  0xe3,0xad,0x4,0xb6,0x55,0xbd,0x82,0x30,0x9e,0x54,0x74,0x8f,0xb2,0x12,0x93,0xd6,
  0x7b,0x1d,0x7a,0x3e,0x1f,0x95,0xab,0x4f,0x29,0xf2,0x60,0xf6,0xcf,0xeb,0x8b,0x1a,
  0x25,0xe2,0x57,0x78,0x2f,0xe,0xd,0x18,0x63,0x19,0x1f,0x6a,0xd3,0x5d,0x73,0xb9,
  0xdb,0xed,0x1a,0x1b,0x48,0xba,0x65,0x95,0x4a,0xd3,0x66,0x46,0x9d,0x38,0x67,0xbe,
  0x1f,0x9f,0xaf,0x29,0x40,0x2e,0x9e,0x52,0xff,0xd9,0x52,0x14,0xa,0xb9,0x8f,0xc3,
  0x23,0xd8,0xad,0x18,0xb6,0xf9,0x11,0x9a,0x72,0xc6,0xe3,0x2a,0x5d,0x91,0x47,0xda,
  0xd8,0x56,0xf3,0xb4,0x3b,0x68,0xd7,0x2b,0x6e,0x8b,0x8d,0x90,0xdf,0xbc,0x1,0xf2,
  0x9b,0x5d,0x82,0x3c,0xf8,0xef,0x20,0x1f,0x62,0x16,0xb9,0xd8,0x96,0x91,0x20,0xa2,
  0x36,0xf6,0xf9,0xc1,0x3b,0x10,0x84,0x6c,0x77,0x9e,0xe6,0xcc,0xca,0x6f,0xd1,0x85,
  0xbd,0x79,0xed,0x9e,0xa9,0xeb,0x7,0x94,0xea,0xf6,0x5b,0x36,0x56,0xb5,0x4b,0xa,
  0x1c,0xc,0xb9,0x3,0x25,0xf6,0x1e,0x6c,0x34,0x1f,0xe0,0xfc,0xf8,0x0,0x5d,0x35,
  0x2,0x93,0x56,0xcd,0x3e,0xe4,0x48,0x58,0x3b,0x30,0x6a,0xd4,0xee,0xd0,0x22,0xf9,
  0x53,0x46,0xbd,0x5b,0x4,0xb4,0x27,0x83,0xc1,0x69,0xaf,0xd1,0xf8,0x97,0x9c,0x49,
  0x43,0x73,0x83,0xa5,0x57,0xbf,0x5c,0xc1,0xa0,0x5d,0xb,0x4c,0x6c,0xd3,0xbb,0xc5,
  0xe5,0x2f,0xca,0x60,0xe9,0xf4,0xd2,0xac,0x1,0x53,0x7d,0x56,0x2e,0x56,0x2f,0x48,
  0x10,0xfa,0x51,0x34,0x96,0x70,0x2c,0xe,0x3d,0xec,0xf0,0x79,0xfe,0x9a,0x57,0x75,
  0x64,0xde,0xea,0x7e,0x24,0x79,0x30,0x54,0x57,0x24,0x38,0xaa,0x94,0x84,0x3e,0x71,
  0x65,0x59,0x9a,0x39,0x29,0x5,0x66,0x61,0x32,0xed,0xd2,0xd7,0xd5,0x3b,0x6c,0x43,
  0xb5,0x56,0xa3,0xa4,0x51,0xb8,0xcc,0xe6,0x4f,0xc5,0xe9,0x7d,0x31,0xf9,0xd4,0xd2,
  0x17,0xb,0x7d,0x6a,0xa6,0x88,0x2c,0xf6,0xfe,0x0,0x26,0xd8,0xe8,0xc4,
    // main.qml
  0x0,0x0,0x2,0xf6,
  0x0,
  0x0,0xa,0x97,0x78,0xda,0xcd,0x56,0x5d,0x6f,0xda,0x30,0x14,0x7d,0xe7,0x57,0x5c,
  0xa5,0xf,0x2d,0x9a,0x96,0xc2,0xb4,0xa2,0x2d,0x12,0x9a,0x28,0x6c,0x5a,0xa5,0x6e,
  0x52,0x4b,0xbb,0xbe,0xec,0xc5,0x49,0xc,0x58,0x35,0x76,0x16,0xdf,0x94,0x7e,0x88,
  0xff,0xbe,0x1b,0x27,0x40,0x3e,0x29,0xeb,0xd3,0xfc,0xd0,0xc6,0xd7,0x27,0xce,0x3d,
  0xbe,0xc7,0xe7,0x22,0x96,0x91,0x8e,0x11,0xae,0xf0,0x2a,0x11,0xc1,0x3d,0x7c,0x70,
  0xfb,0x67,0x1d,0x51,0x8a,0xb9,0x63,0xad,0x30,0xd6,0xd2,0x34,0x2e,0x5e,0xb2,0x27,
  0x9d,0xa0,0x81,0x7e,0x61,0xed,0xd6,0xf8,0x13,0x8e,0x3c,0x40,0x8a,0xf6,0x3a,0x9d,
  0x51,0x14,0x49,0x11,0x30,0x14,0x5a,0xdd,0x9,0x15,0xea,0x15,0xbc,0x74,0x80,0x86,
  0x8,0x3d,0x58,0xd9,0x80,0x9d,0xae,0x44,0x88,0xb,0xf,0x3e,0xf7,0x7a,0x76,0xba,
  0xe0,0x62,0xbe,0x40,0xf,0x6,0xf9,0xfc,0x41,0x18,0xe1,0x4b,0xee,0x1,0xc6,0x9,
  0xb7,0x11,0x14,0x98,0xce,0xff,0x98,0x9b,0xf8,0xc4,0xb9,0x9d,0x9e,0xc3,0x84,0x3f,
  0x88,0x80,0xc3,0xf,0xad,0x4,0xea,0xd8,0xe9,0x5a,0x94,0xfd,0x13,0xc5,0x3a,0xe2,
  0x31,0x3e,0x81,0xaf,0xb5,0x4,0x61,0x72,0x88,0x50,0x73,0xf,0x66,0x4c,0x1a,0xbe,
  0x83,0x2e,0x38,0xb,0x79,0xec,0xc1,0xd,0x21,0xcf,0x59,0x9c,0xe7,0x9a,0x8e,0x6b,
  0xbd,0xca,0xd8,0x16,0x62,0xe9,0x60,0x2a,0x58,0xe8,0xd8,0xb8,0x33,0x21,0xa5,0x7,
  0x11,0x8b,0xb9,0xc2,0x46,0xc0,0x92,0xc5,0x73,0xa1,0x8c,0x7,0xfd,0x5e,0x69,0xbd,
  0x34,0xb9,0x64,0x3e,0x97,0x95,0x2f,0x58,0xb6,0xfc,0x11,0x5f,0x25,0x5b,0x1c,0x33,
  0x2a,0x9b,0xeb,0x6b,0x19,0x16,0x8e,0xac,0xb6,0x1e,0x89,0x47,0x2e,0xa7,0xe2,0x99,
  0xce,0xb1,0xff,0xa9,0x6,0xc9,0xe8,0x5a,0x62,0x77,0x59,0x75,0x6a,0x3b,0xad,0xdb,
  0x99,0x9c,0x27,0x88,0x5a,0x35,0x50,0x49,0xb,0xbf,0xcc,0xf2,0xce,0x30,0x2d,0x64,
  0x8b,0x75,0x82,0x2f,0x39,0xf7,0x29,0xea,0x8,0x76,0x71,0xa7,0xb,0xde,0x76,0x85,
  0x91,0xf8,0x8a,0x4b,0xb5,0x7d,0x17,0xa4,0x29,0x99,0xea,0x8a,0x87,0xe5,0xed,0x6b,
  0x48,0xad,0xc6,0x24,0xda,0xfb,0x14,0x57,0x27,0x60,0x49,0xcc,0xe0,0xa4,0xb8,0x43,
  0xb7,0x5,0x97,0x8e,0xc4,0xf8,0x39,0xd0,0x35,0x94,0xfe,0xee,0xa5,0x93,0x6e,0xeb,
  0x3b,0x25,0xf2,0xc3,0x82,0x4a,0xab,0x63,0xd,0x9c,0x96,0xe,0xfd,0x3a,0x1d,0xd1,
  0x5b,0x3e,0xdf,0xa8,0xa0,0x7a,0xfd,0xeb,0x91,0xb7,0xe8,0xa3,0x28,0xf5,0x6b,0x3e,
  0x8b,0xb9,0x59,0x34,0xd4,0xb2,0x50,0xa1,0x2,0xc3,0x38,0xc3,0x67,0xb7,0xc3,0x54,
  0x8,0xee,0x92,0xc9,0x9e,0xd6,0xbb,0x6b,0x3f,0xd,0xc8,0xe2,0xe4,0x2f,0xc1,0x57,
  0x85,0x84,0xf6,0xde,0xec,0x7d,0xb7,0x7a,0xfb,0x70,0x29,0xc,0x56,0x36,0xdd,0xdc,
  0x80,0xd0,0xa6,0xb8,0x1,0x94,0x96,0x97,0x3a,0xe4,0xb2,0x44,0x2b,0x3,0x9b,0x12,
  0xca,0x44,0x2c,0xb0,0xe,0xb6,0xcf,0x4e,0x68,0x23,0x3e,0x67,0x48,0xf7,0xdb,0xba,
  0x72,0xba,0xcb,0x24,0xf,0x35,0x9c,0x7c,0xee,0xc1,0xe5,0xd4,0x5c,0x1b,0xad,0x61,
  0x33,0x90,0x97,0x25,0x3b,0x61,0xc8,0xe,0xad,0xfb,0xe9,0x29,0x7c,0x5d,0x46,0x64,
  0xc6,0xa4,0x46,0xe4,0x7,0x99,0xdf,0xe6,0xac,0x3,0x2a,0x0,0x8f,0x2f,0x54,0xa3,
  0xcd,0x56,0xa5,0xf3,0x53,0x43,0x6a,0x94,0xf9,0xd1,0xd1,0xff,0xb4,0x25,0xf1,0xd0,
  0xfd,0x9d,0x9,0x7,0x8e,0xab,0x86,0x71,0xc,0xa8,0xc1,0xe7,0x54,0x4d,0x30,0x1,
  0x53,0x8a,0x42,0x6e,0x93,0x89,0x10,0xf8,0x99,0xcc,0x93,0xc9,0x11,0x79,0x89,0x5a,
  0x52,0x1e,0xd4,0x2e,0xe8,0xc3,0xae,0x9d,0x7f,0x1f,0xdb,0x24,0x6b,0xaf,0x5,0x5a,
  0x6a,0xea,0x2b,0xce,0xd1,0x60,0x30,0x70,0x5e,0x77,0xe3,0x41,0xd,0xb2,0xed,0x80,
  0x95,0xf2,0x4,0x3a,0x51,0x8,0xc3,0xe1,0x10,0x7a,0x87,0xaa,0x9d,0x4a,0x40,0xe4,
  0x31,0x31,0xe0,0xb3,0x2c,0xd3,0x99,0xd6,0xf8,0xbf,0xb7,0xbd,0x2c,0x65,0x3a,0xc2,
  0x2e,0xbc,0x2b,0x9b,0xef,0xb6,0x3b,0xb4,0x34,0x6,0x1d,0x45,0x3c,0x74,0xba,0xdd,
  0xb6,0xa2,0x54,0xf6,0x72,0x8e,0x3e,0x8e,0x47,0xdf,0xce,0x7a,0xe,0x34,0xd6,0x6b,
  0x8f,0xb6,0x2f,0x90,0x2f,0xe1,0xa5,0xa5,0x71,0xee,0x7b,0xf1,0x10,0xfe,0xb9,0xa9,
  0xe5,0x7,0xd0,0xa4,0x82,0x7f,0x10,0xdd,0x1e,0x75,0x8c,0x35,0xfd,0x8e,0x53,0x54,
  0x4e,0x97,0x3c,0x96,0x9e,0x25,0xc7,0x72,0x1f,0x24,0xf9,0x8c,0x12,0xd4,0xef,0x6d,
  0x3b,0xd9,0xb4,0xf2,0x62,0x17,0x3d,0xa8,0xe5,0xb4,0xb5,0x98,0x75,0x67,0xdd,0xf9,
  0xb,0xc1,0x3c,0xb6,0xd5,
  
};

static const unsigned char qt_resource_name[] = {
  // UsbDeviceDelegate.qml
  0x0,0x15,
  0x1,0x51,0xdd,0x5c,
  0x0,0x55,
  0x0,0x73,0x0,0x62,0x0,0x44,0x0,0x65,0x0,0x76,0x0,0x69,0x0,0x63,0x0,0x65,0x0,0x44,0x0,0x65,0x0,0x6c,0x0,0x65,0x0,0x67,0x0,0x61,0x0,0x74,0x0,0x65,
  0x0,0x2e,0x0,0x71,0x0,0x6d,0x0,0x6c,
    // main.qml
  0x0,0x8,
  0x8,0x1,0x5a,0x5c,
  0x0,0x6d,
  0x0,0x61,0x0,0x69,0x0,0x6e,0x0,0x2e,0x0,0x71,0x0,0x6d,0x0,0x6c,
  
};

static const unsigned char qt_resource_struct[] = {
  // :
  0x0,0x0,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0x1,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/UsbDeviceDelegate.qml
  0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x0,
0x0,0x0,0x1,0x99,0x7e,0xc5,0x7d,0x6e,
  // :/main.qml
  0x0,0x0,0x0,0x30,0x0,0x1,0x0,0x0,0x0,0x1,0x0,0x0,0x3,0x83,
0x0,0x0,0x1,0x99,0x7e,0xc5,0x3a,0xe0,

};

#ifdef QT_NAMESPACE
#  define QT_RCC_PREPEND_NAMESPACE(name) ::QT_NAMESPACE::name
#  define QT_RCC_MANGLE_NAMESPACE0(x) x
#  define QT_RCC_MANGLE_NAMESPACE1(a, b) a##_##b
#  define QT_RCC_MANGLE_NAMESPACE2(a, b) QT_RCC_MANGLE_NAMESPACE1(a,b)
#  define QT_RCC_MANGLE_NAMESPACE(name) QT_RCC_MANGLE_NAMESPACE2( \
        QT_RCC_MANGLE_NAMESPACE0(name), QT_RCC_MANGLE_NAMESPACE0(QT_NAMESPACE))
#else
#   define QT_RCC_PREPEND_NAMESPACE(name) name
#   define QT_RCC_MANGLE_NAMESPACE(name) name
#endif

#if defined(QT_INLINE_NAMESPACE)
inline namespace QT_NAMESPACE {
#elif defined(QT_NAMESPACE)
namespace QT_NAMESPACE {
#endif

bool qRegisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);
bool qUnregisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);

#if defined(__ELF__) || defined(__APPLE__)
static inline unsigned char qResourceFeatureZlib()
{
    extern const unsigned char qt_resourceFeatureZlib;
    return qt_resourceFeatureZlib;
}
#else
unsigned char qResourceFeatureZlib();
#endif

#ifdef QT_NAMESPACE
}
#endif

int QT_RCC_MANGLE_NAMESPACE(qInitResources_qml)();
int QT_RCC_MANGLE_NAMESPACE(qInitResources_qml)()
{
    int version = 3;
    QT_RCC_PREPEND_NAMESPACE(qRegisterResourceData)
        (version, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_qml)();
int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_qml)()
{
    int version = 3;
    version += QT_RCC_PREPEND_NAMESPACE(qResourceFeatureZlib());
    QT_RCC_PREPEND_NAMESPACE(qUnregisterResourceData)
       (version, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

#ifdef __clang__
#   pragma clang diagnostic push
#   pragma clang diagnostic ignored "-Wexit-time-destructors"
#endif

namespace {
   struct initializer {
       initializer() { QT_RCC_MANGLE_NAMESPACE(qInitResources_qml)(); }
       ~initializer() { QT_RCC_MANGLE_NAMESPACE(qCleanupResources_qml)(); }
   } dummy;
}

#ifdef __clang__
#   pragma clang diagnostic pop
#endif
