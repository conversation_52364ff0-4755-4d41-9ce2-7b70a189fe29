@echo off
echo Testing qmake build system...

REM Check if qmake is available
qmake --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo qmake not found in PATH. This is normal if Qt is not installed.
    echo You can still use Qt Creator to open UsbDetect.pro
    goto :end
)

echo qmake found, testing project file...

REM Test qmake project file parsing
qmake -query QT_VERSION
if %ERRORLEVEL% neq 0 (
    echo Failed to query Qt version
    goto :end
)

echo Testing project file syntax...
qmake UsbDetect.pro -o test_makefile
if %ERRORLEVEL% neq 0 (
    echo Project file has syntax errors!
    goto :end
)

echo Project file syntax is correct!
if exist test_makefile del test_makefile

:end
echo.
echo To build the project:
echo 1. Open Qt Creator
echo 2. Open UsbDetect.pro
echo 3. Configure and build
echo.
echo Or use: build_qmake.bat
pause
