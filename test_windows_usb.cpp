// 测试Windows USB检测功能
// 编译: g++ -std=c++17 test_windows_usb.cpp -lsetupapi -o test_windows_usb

#include <iostream>
#include <string>
#include <vector>

#ifdef _WIN32
#include <windows.h>
#include <setupapi.h>
#include <devguid.h>
#include <regstr.h>

std::string wcharToString(const wchar_t* wstr) {
    if (!wstr) return "";
    
    int size = WideCharToMultiByte(CP_UTF8, 0, wstr, -1, nullptr, 0, nullptr, nullptr);
    if (size <= 0) return "";
    
    std::vector<char> buffer(size);
    WideCharToMultiByte(CP_UTF8, 0, wstr, -1, buffer.data(), size, nullptr, nullptr);
    return std::string(buffer.data());
}

std::string getDeviceProperty(HDEVINFO deviceInfoSet, SP_DEVINFO_DATA& deviceInfoData, DWORD property) {
    DWORD dataType;
    DWORD requiredSize = 0;
    
    if (!SetupDiGetDeviceRegistryProperty(deviceInfoSet, &deviceInfoData, property, 
                                        &dataType, nullptr, 0, &requiredSize)) {
        DWORD error = GetLastError();
        if (error != ERROR_INSUFFICIENT_BUFFER) {
            return "";
        }
    }
    
    if (requiredSize == 0) {
        return "";
    }
    
    std::vector<BYTE> buffer(requiredSize);
    if (SetupDiGetDeviceRegistryProperty(deviceInfoSet, &deviceInfoData, property,
                                       &dataType, buffer.data(), requiredSize, nullptr)) {
        if (dataType == REG_SZ || dataType == REG_MULTI_SZ) {
            return wcharToString((wchar_t*)buffer.data());
        }
    }
    
    return "";
}

void testMethod1() {
    std::cout << "\n=== Testing Method 1: GUID_DEVCLASS_USB ===" << std::endl;
    
    HDEVINFO deviceInfoSet = SetupDiGetClassDevs(&GUID_DEVCLASS_USB, nullptr, nullptr, 
                                                  DIGCF_PRESENT);
    
    if (deviceInfoSet == INVALID_HANDLE_VALUE) {
        std::cout << "Failed to get USB device info set" << std::endl;
        return;
    }

    SP_DEVINFO_DATA deviceInfoData;
    deviceInfoData.cbSize = sizeof(SP_DEVINFO_DATA);
    
    int count = 0;
    for (DWORD i = 0; SetupDiEnumDeviceInfo(deviceInfoSet, i, &deviceInfoData); i++) {
        std::string hardwareId = getDeviceProperty(deviceInfoSet, deviceInfoData, SPDRP_HARDWAREID);
        
        if (hardwareId.find("USB") != std::string::npos) {
            count++;
            std::string desc = getDeviceProperty(deviceInfoSet, deviceInfoData, SPDRP_DEVICEDESC);
            std::cout << "Device " << count << ": " << desc << std::endl;
            std::cout << "  Hardware ID: " << hardwareId << std::endl;
        }
    }
    
    SetupDiDestroyDeviceInfoList(deviceInfoSet);
    std::cout << "Method 1 found " << count << " USB devices" << std::endl;
}

void testMethod2() {
    std::cout << "\n=== Testing Method 2: All Classes Filter ===" << std::endl;
    
    HDEVINFO deviceInfoSet = SetupDiGetClassDevs(nullptr, nullptr, nullptr, 
                                                  DIGCF_PRESENT | DIGCF_ALLCLASSES);
    
    if (deviceInfoSet == INVALID_HANDLE_VALUE) {
        std::cout << "Failed to get device info set" << std::endl;
        return;
    }

    SP_DEVINFO_DATA deviceInfoData;
    deviceInfoData.cbSize = sizeof(SP_DEVINFO_DATA);
    
    int count = 0;
    for (DWORD i = 0; SetupDiEnumDeviceInfo(deviceInfoSet, i, &deviceInfoData); i++) {
        std::string hardwareId = getDeviceProperty(deviceInfoSet, deviceInfoData, SPDRP_HARDWAREID);
        
        if (hardwareId.find("USB\\VID_") != std::string::npos) {
            count++;
            std::string desc = getDeviceProperty(deviceInfoSet, deviceInfoData, SPDRP_DEVICEDESC);
            std::cout << "Device " << count << ": " << desc << std::endl;
            std::cout << "  Hardware ID: " << hardwareId << std::endl;
            
            // Extract VID/PID
            size_t vidPos = hardwareId.find("VID_");
            size_t pidPos = hardwareId.find("PID_");
            if (vidPos != std::string::npos && pidPos != std::string::npos) {
                std::string vid = hardwareId.substr(vidPos + 4, 4);
                std::string pid = hardwareId.substr(pidPos + 4, 4);
                std::cout << "  VID: " << vid << ", PID: " << pid << std::endl;
            }
        }
    }
    
    SetupDiDestroyDeviceInfoList(deviceInfoSet);
    std::cout << "Method 2 found " << count << " USB devices" << std::endl;
}

int main() {
    std::cout << "Windows USB Device Detection Test" << std::endl;
    std::cout << "=================================" << std::endl;
    
    testMethod1();
    testMethod2();
    
    std::cout << "\nTest completed. Press Enter to exit..." << std::endl;
    std::cin.get();
    
    return 0;
}

#else
int main() {
    std::cout << "This test is only for Windows platform" << std::endl;
    return 0;
}
#endif
