import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import UsbDetect 1.0

ApplicationWindow {
    id: window
    width: 900
    height: 600
    visible: true
    title: qsTr("USB Device Monitor")
    
    property bool isMonitoring: false
    
    header: ToolBar {
        RowLayout {
            anchors.fill: parent
            anchors.margins: 10
            
            Label {
                text: qsTr("USB Device Monitor")
                font.bold: true
                font.pixelSize: 18
                Layout.fillWidth: true
            }
            
            Button {
                id: monitorButton
                text: isMonitoring ? qsTr("Stop Monitoring") : qsTr("Start Monitoring")
                highlighted: isMonitoring
                onClicked: {
                    if (isMonitoring) {
                        usbMonitor.stopMonitoring()
                        isMonitoring = false
                    } else {
                        usbMonitor.startMonitoring()
                        isMonitoring = true
                    }
                }
            }
            
            Button {
                text: qsTr("Refresh")
                onClicked: usbMonitor.refreshDevices()
            }
        }
    }
    
    ScrollView {
        anchors.fill: parent
        anchors.margins: 10
        
        ListView {
            id: deviceListView
            model: usbMonitor.devices
            spacing: 10
            
            delegate: UsbDeviceDelegate {
                width: deviceListView.width
                device: modelData
            }
            
            // Empty state
            Label {
                anchors.centerIn: parent
                text: qsTr("No USB devices detected.\nClick 'Start Monitoring' to begin scanning.")
                horizontalAlignment: Text.AlignHCenter
                color: "#666"
                font.pixelSize: 16
                visible: deviceListView.count === 0
            }
        }
    }
    
    // Status bar
    footer: ToolBar {
        RowLayout {
            anchors.fill: parent
            anchors.margins: 10
            
            Label {
                text: qsTr("Status: ") + (isMonitoring ? qsTr("Monitoring") : qsTr("Stopped"))
                color: isMonitoring ? "#4CAF50" : "#666"
            }
            
            Item { Layout.fillWidth: true }
            
            Label {
                text: qsTr("Devices: ") + deviceListView.count
                color: "#666"
            }
        }
    }
    
    Component.onCompleted: {
        // Auto-start monitoring
        usbMonitor.startMonitoring()
        isMonitoring = true
    }
}
