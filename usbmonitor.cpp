#include "usbmonitor.h"
#include <QDebug>
#include <QRegularExpression>

UsbMonitor::UsbMonitor(QObject *parent)
    : QObject(parent)
    , m_timer(new QTimer(this))
#ifdef __linux__
    , m_udev(nullptr)
    , m_monitor(nullptr)
#endif
{
    connect(m_timer, &QTimer::timeout, this, &UsbMonitor::checkForDeviceChanges);
    
#ifdef __linux__
    m_udev = udev_new();
    if (!m_udev) {
        qWarning() << "Cannot create udev context";
    }
#endif
}

UsbMonitor::~UsbMonitor()
{
#ifdef __linux__
    if (m_monitor) {
        udev_monitor_unref(m_monitor);
    }
    if (m_udev) {
        udev_unref(m_udev);
    }
#endif
    qDeleteAll(m_devices);
}

QQmlListProperty<UsbDevice> UsbMonitor::devices()
{
    return QQmlListProperty<UsbDevice>(this, nullptr, &UsbMonitor::devicesCount, &UsbMonitor::deviceAt);
}

void UsbMonitor::startMonitoring()
{
    qDebug() << "Starting USB monitoring...";
    refreshDevices();
    m_timer->start(2000); // Check every 2 seconds
}

void UsbMonitor::stopMonitoring()
{
    qDebug() << "Stopping USB monitoring...";
    m_timer->stop();
}

void UsbMonitor::refreshDevices()
{
    qDebug() << "Refreshing USB devices...";
    
    // Clear existing devices
    qDeleteAll(m_devices);
    m_devices.clear();
    
    scanForUsbDevices();
    emit devicesChanged();
}

void UsbMonitor::checkForDeviceChanges()
{
    // For simplicity, we'll just refresh the entire list
    // In a production app, you might want to implement more sophisticated change detection
    refreshDevices();
}

void UsbMonitor::scanForUsbDevices()
{
#ifdef _WIN32
    scanWindowsUsbDevices();
#elif defined(__linux__)
    scanLinuxUsbDevices();
#else
    qWarning() << "USB monitoring not implemented for this platform";
#endif
}

QString UsbMonitor::getDeviceClass(int classCode)
{
    switch (classCode) {
        case 0x01: return "Audio";
        case 0x02: return "Communications";
        case 0x03: return "HID (Human Interface Device)";
        case 0x05: return "Physical";
        case 0x06: return "Image";
        case 0x07: return "Printer";
        case 0x08: return "Mass Storage";
        case 0x09: return "Hub";
        case 0x0A: return "CDC-Data";
        case 0x0B: return "Smart Card";
        case 0x0D: return "Content Security";
        case 0x0E: return "Video";
        case 0x0F: return "Personal Healthcare";
        case 0x10: return "Audio/Video";
        case 0x11: return "Billboard";
        case 0xDC: return "Diagnostic";
        case 0xE0: return "Wireless Controller";
        case 0xEF: return "Miscellaneous";
        case 0xFE: return "Application Specific";
        case 0xFF: return "Vendor Specific";
        default: return QString("Unknown (0x%1)").arg(classCode, 2, 16, QChar('0'));
    }
}

qsizetype UsbMonitor::devicesCount(QQmlListProperty<UsbDevice> *list)
{
    UsbMonitor *monitor = qobject_cast<UsbMonitor*>(list->object);
    return monitor ? monitor->m_devices.count() : 0;
}

UsbDevice *UsbMonitor::deviceAt(QQmlListProperty<UsbDevice> *list, qsizetype index)
{
    UsbMonitor *monitor = qobject_cast<UsbMonitor*>(list->object);
    return (monitor && index >= 0 && index < monitor->m_devices.count()) ? monitor->m_devices.at(index) : nullptr;
}

#ifdef _WIN32
void UsbMonitor::scanWindowsUsbDevices()
{
    // Try the primary method first
    scanWindowsUsbDevicesPrimary();

    // If no devices found, try the fallback method
    if (m_devices.isEmpty()) {
        qDebug() << "Primary method found no devices, trying fallback method...";
        scanWindowsUsbDevicesFallback();
    }
}

void UsbMonitor::scanWindowsUsbDevicesPrimary()
{
    // Use GUID_DEVCLASS_USB for better compatibility across Windows versions
    HDEVINFO deviceInfoSet = SetupDiGetClassDevs(&GUID_DEVCLASS_USB, nullptr, nullptr,
                                                  DIGCF_PRESENT);

    if (deviceInfoSet == INVALID_HANDLE_VALUE) {
        qWarning() << "Failed to get USB device info set";
        return;
    }

    SP_DEVINFO_DATA deviceInfoData;
    deviceInfoData.cbSize = sizeof(SP_DEVINFO_DATA);

    for (DWORD i = 0; SetupDiEnumDeviceInfo(deviceInfoSet, i, &deviceInfoData); i++) {
        QString hardwareId = getWindowsDeviceProperty(deviceInfoSet, deviceInfoData, SPDRP_HARDWAREID);

        // Only process USB devices (check if hardware ID contains USB)
        if (!hardwareId.contains("USB", Qt::CaseInsensitive)) {
            continue;
        }

        QString devicePath = getWindowsDeviceProperty(deviceInfoSet, deviceInfoData, SPDRP_PHYSICAL_DEVICE_OBJECT_NAME);
        QString manufacturer = getWindowsDeviceProperty(deviceInfoSet, deviceInfoData, SPDRP_MFG);
        QString friendlyName = getWindowsDeviceProperty(deviceInfoSet, deviceInfoData, SPDRP_FRIENDLYNAME);
        QString deviceDesc = getWindowsDeviceProperty(deviceInfoSet, deviceInfoData, SPDRP_DEVICEDESC);

        // Extract VID and PID from hardware ID
        QString vid, pid;
        QRegularExpression vidPidRegex(R"(VID_([0-9A-Fa-f]{4}).*PID_([0-9A-Fa-f]{4}))");
        QRegularExpressionMatch match = vidPidRegex.match(hardwareId);
        if (match.hasMatch()) {
            vid = match.captured(1).toUpper();
            pid = match.captured(2).toUpper();
        }

        // Get device class from hardware ID or use a default approach
        QString deviceClass = "Unknown";

        // Try to determine device class from hardware ID patterns
        if (hardwareId.contains("HID", Qt::CaseInsensitive)) {
            deviceClass = "HID (Human Interface Device)";
        } else if (hardwareId.contains("STORAGE", Qt::CaseInsensitive) ||
                   hardwareId.contains("DISK", Qt::CaseInsensitive)) {
            deviceClass = "Mass Storage";
        } else if (hardwareId.contains("AUDIO", Qt::CaseInsensitive)) {
            deviceClass = "Audio";
        } else if (hardwareId.contains("VIDEO", Qt::CaseInsensitive) ||
                   hardwareId.contains("CAMERA", Qt::CaseInsensitive)) {
            deviceClass = "Video";
        } else if (hardwareId.contains("HUB", Qt::CaseInsensitive)) {
            deviceClass = "Hub";
        } else if (hardwareId.contains("PRINTER", Qt::CaseInsensitive)) {
            deviceClass = "Printer";
        } else {
            deviceClass = "USB Device";
        }

        QString product = friendlyName.isEmpty() ? deviceDesc : friendlyName;
        if (product.isEmpty()) {
            product = "USB Device";
        }

        if (!vid.isEmpty() && !pid.isEmpty()) {
            UsbDevice *device = new UsbDevice(vid, pid, devicePath, manufacturer,
                                            product, deviceClass, "Connected", this);
            m_devices.append(device);
        }
    }

    SetupDiDestroyDeviceInfoList(deviceInfoSet);
}

QString UsbMonitor::getWindowsDeviceProperty(HDEVINFO deviceInfoSet, SP_DEVINFO_DATA &deviceInfoData, DWORD property)
{
    DWORD dataType;
    DWORD requiredSize = 0;

    // Get required buffer size
    if (!SetupDiGetDeviceRegistryProperty(deviceInfoSet, &deviceInfoData, property,
                                        &dataType, nullptr, 0, &requiredSize)) {
        DWORD error = GetLastError();
        if (error != ERROR_INSUFFICIENT_BUFFER) {
            return QString();
        }
    }

    if (requiredSize == 0) {
        return QString();
    }

    QByteArray buffer(requiredSize, 0);
    if (SetupDiGetDeviceRegistryProperty(deviceInfoSet, &deviceInfoData, property,
                                       &dataType, (PBYTE)buffer.data(), requiredSize, nullptr)) {
        if (dataType == REG_SZ || dataType == REG_MULTI_SZ) {
            // Handle Unicode strings
            QString result = QString::fromWCharArray((wchar_t*)buffer.data());
            return result.trimmed();
        } else if (dataType == REG_DWORD) {
            // Handle DWORD values
            DWORD value = *((DWORD*)buffer.data());
            return QString::number(value);
        }
    }

    return QString();
}

void UsbMonitor::scanWindowsUsbDevicesFallback()
{
    qDebug() << "Using fallback Windows USB detection method...";

    // 扫描所有设备，然后过滤USB设备
    HDEVINFO deviceInfoSet = SetupDiGetClassDevs(nullptr, nullptr, nullptr,
                                                  DIGCF_PRESENT | DIGCF_ALLCLASSES);

    if (deviceInfoSet == INVALID_HANDLE_VALUE) {
        qWarning() << "Failed to get device info set for fallback method";
        return;
    }

    SP_DEVINFO_DATA deviceInfoData;
    deviceInfoData.cbSize = sizeof(SP_DEVINFO_DATA);

    for (DWORD i = 0; SetupDiEnumDeviceInfo(deviceInfoSet, i, &deviceInfoData); i++) {
        QString hardwareId = getWindowsDeviceProperty(deviceInfoSet, deviceInfoData, SPDRP_HARDWAREID);

        // 只处理USB设备
        if (!hardwareId.contains("USB\\VID_", Qt::CaseInsensitive)) {
            continue;
        }

        // 提取VID和PID
        QRegularExpression vidPidRegex(R"(USB\\VID_([0-9A-Fa-f]{4})&PID_([0-9A-Fa-f]{4}))");
        QRegularExpressionMatch match = vidPidRegex.match(hardwareId);

        if (!match.hasMatch()) {
            continue;
        }

        QString vid = match.captured(1).toUpper();
        QString pid = match.captured(2).toUpper();

        QString manufacturer = getWindowsDeviceProperty(deviceInfoSet, deviceInfoData, SPDRP_MFG);
        QString friendlyName = getWindowsDeviceProperty(deviceInfoSet, deviceInfoData, SPDRP_FRIENDLYNAME);
        QString deviceDesc = getWindowsDeviceProperty(deviceInfoSet, deviceInfoData, SPDRP_DEVICEDESC);

        // 简单的设备类别判断
        QString deviceClass = "USB Device";
        if (hardwareId.contains("HID", Qt::CaseInsensitive)) {
            deviceClass = "HID (Human Interface Device)";
        } else if (hardwareId.contains("STORAGE", Qt::CaseInsensitive) ||
                   hardwareId.contains("DISK", Qt::CaseInsensitive)) {
            deviceClass = "Mass Storage";
        } else if (hardwareId.contains("AUDIO", Qt::CaseInsensitive)) {
            deviceClass = "Audio";
        } else if (hardwareId.contains("VIDEO", Qt::CaseInsensitive)) {
            deviceClass = "Video";
        } else if (hardwareId.contains("HUB", Qt::CaseInsensitive)) {
            deviceClass = "Hub";
        }

        QString product = friendlyName.isEmpty() ? deviceDesc : friendlyName;
        if (product.isEmpty()) {
            product = "USB Device";
        }

        if (manufacturer.isEmpty()) {
            manufacturer = "Unknown";
        }

        // 创建设备对象
        UsbDevice *device = new UsbDevice(vid, pid, hardwareId, manufacturer,
                                        product, deviceClass, "Connected", this);
        m_devices.append(device);

        qDebug() << "Found USB device:" << vid << pid << product;
    }

    SetupDiDestroyDeviceInfoList(deviceInfoSet);
    qDebug() << "Fallback method found" << m_devices.count() << "USB devices";
}
#endif

#ifdef __linux__
void UsbMonitor::scanLinuxUsbDevices()
{
    if (!m_udev) {
        qWarning() << "udev context not available";
        return;
    }

    struct udev_enumerate *enumerate = udev_enumerate_new(m_udev);
    if (!enumerate) {
        qWarning() << "Cannot create udev enumerate";
        return;
    }

    // Filter for USB devices
    udev_enumerate_add_match_subsystem(enumerate, "usb");
    udev_enumerate_add_match_property(enumerate, "DEVTYPE", "usb_device");
    udev_enumerate_scan_devices(enumerate);

    struct udev_list_entry *devices = udev_enumerate_get_list_entry(enumerate);
    struct udev_list_entry *entry;

    udev_list_entry_foreach(entry, devices) {
        const char *path = udev_list_entry_get_name(entry);
        struct udev_device *device = udev_device_new_from_syspath(m_udev, path);

        if (!device) {
            continue;
        }

        // Get device properties
        const char *vid_str = udev_device_get_sysattr_value(device, "idVendor");
        const char *pid_str = udev_device_get_sysattr_value(device, "idProduct");
        const char *manufacturer = udev_device_get_sysattr_value(device, "manufacturer");
        const char *product = udev_device_get_sysattr_value(device, "product");
        const char *bDeviceClass = udev_device_get_sysattr_value(device, "bDeviceClass");
        const char *devpath = udev_device_get_devpath(device);

        QString vid = vid_str ? QString(vid_str).toUpper() : "Unknown";
        QString pid = pid_str ? QString(pid_str).toUpper() : "Unknown";
        QString mfg = manufacturer ? QString::fromUtf8(manufacturer) : "Unknown";
        QString prod = product ? QString::fromUtf8(product) : "Unknown";
        QString devicePath = devpath ? QString(devpath) : "Unknown";

        // Parse device class
        int classCode = 0;
        if (bDeviceClass) {
            bool ok;
            classCode = QString(bDeviceClass).toInt(&ok, 16);
            if (!ok) classCode = 0;
        }
        QString deviceClass = getDeviceClass(classCode);

        // Check if device is connected
        const char *driver = udev_device_get_driver(device);
        QString status = driver ? "Connected" : "Disconnected";

        if (!vid.isEmpty() && !pid.isEmpty() && vid != "Unknown" && pid != "Unknown") {
            UsbDevice *usbDevice = new UsbDevice(vid, pid, devicePath, mfg,
                                                prod, deviceClass, status, this);
            m_devices.append(usbDevice);
        }

        udev_device_unref(device);
    }

    udev_enumerate_unref(enumerate);
}
#endif
