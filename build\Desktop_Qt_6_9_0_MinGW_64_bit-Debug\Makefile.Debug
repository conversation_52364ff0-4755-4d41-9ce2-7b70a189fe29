#############################################################################
# Makefile for building: UsbDetect
# Generated by qmake (3.1) (Qt 6.9.0)
# Project:  ..\..\UsbDetect.pro
# Template: app
#############################################################################

MAKEFILE      = Makefile.Debug

EQ            = =

####### Compiler, tools and options

CC            = gcc
CXX           = g++
DEFINES       = -DUNICODE -D_UNICODE -DWIN32 -DMINGW_HAS_SECURE_API=1 -D_WIN32 -DUNICODE -D_UNICODE -DWINVER=0x0600 -D_WIN32_WINNT=0x0600 -DQT_QML_DEBUG -DQT_QUICK_LIB -DQT_OPENGL_LIB -DQT_GUI_LIB -DQT_QMLMETA_LIB -DQT_QMLMODELS_LIB -DQT_QMLWORKERSCRIPT_LIB -DQT_QML_LIB -DQT_QMLINTEGRATION_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB -DQT_NEEDS_QMAIN
CFLAGS        = -fno-keep-inline-dllexport -g -Wall -Wextra -Wextra $(DEFINES)
CXXFLAGS      = -fno-keep-inline-dllexport -g -std=gnu++1z -Wall -Wextra -Wextra -fexceptions -mthreads $(DEFINES)
INCPATH       = -I../../../UsbDetect -I. -IC:/Qt/6.9.0/mingw_64/include -IC:/Qt/6.9.0/mingw_64/include/QtQuick -IC:/Qt/6.9.0/mingw_64/include/QtOpenGL -IC:/Qt/6.9.0/mingw_64/include/QtGui -IC:/Qt/6.9.0/mingw_64/include/QtQmlMeta -IC:/Qt/6.9.0/mingw_64/include/QtQmlModels -IC:/Qt/6.9.0/mingw_64/include/QtQmlWorkerScript -IC:/Qt/6.9.0/mingw_64/include/QtQml -IC:/Qt/6.9.0/mingw_64/include/QtQmlIntegration -IC:/Qt/6.9.0/mingw_64/include/QtNetwork -IC:/Qt/6.9.0/mingw_64/include/QtCore -Idebug -I/include -IC:/Qt/6.9.0/mingw_64/mkspecs/win32-g++ 
LINKER      =        g++
LFLAGS        =        -Wl,-subsystem,windows -mthreads
LIBS        =        -lsetupapi -ladvapi32 -lole32 C:\Qt\6.9.0\mingw_64\lib\libQt6Quick.a C:\Qt\6.9.0\mingw_64\lib\libQt6OpenGL.a C:\Qt\6.9.0\mingw_64\lib\libQt6Gui.a C:\Qt\6.9.0\mingw_64\lib\libQt6QmlMeta.a C:\Qt\6.9.0\mingw_64\lib\libQt6QmlModels.a C:\Qt\6.9.0\mingw_64\lib\libQt6QmlWorkerScript.a C:\Qt\6.9.0\mingw_64\lib\libQt6Qml.a C:\Qt\6.9.0\mingw_64\lib\libQt6Network.a C:\Qt\6.9.0\mingw_64\lib\libQt6Core.a -lmingw32 C:\Qt\6.9.0\mingw_64\lib\libQt6EntryPoint.a -lshell32  
QMAKE         = C:\Qt\6.9.0\mingw_64\bin\qmake.exe
DEL_FILE      = del
CHK_DIR_EXISTS= if not exist
MKDIR         = mkdir
COPY          = copy /y
COPY_FILE     = copy /y
COPY_DIR      = xcopy /s /q /y /i
INSTALL_FILE  = copy /y
INSTALL_PROGRAM = copy /y
INSTALL_DIR   = xcopy /s /q /y /i
QINSTALL      = C:\Qt\6.9.0\mingw_64\bin\qmake.exe -install qinstall
QINSTALL_PROGRAM = C:\Qt\6.9.0\mingw_64\bin\qmake.exe -install qinstall -exe
DEL_FILE      = del
SYMLINK       = $(QMAKE) -install ln -f -s
DEL_DIR       = rmdir
MOVE          = move
IDC           = idc
IDL           = midl
ZIP           = zip -r -9
DEF_FILE      = 
RES_FILE      = 
SED           = $(QMAKE) -install sed
MOVE          = move

####### Output directory

OBJECTS_DIR   = debug

####### Files

SOURCES       = ..\..\main.cpp \
		..\..\usbmonitor.cpp \
		..\..\usbdevice.cpp debug\qrc_qml.cpp \
		debug\moc_usbmonitor.cpp \
		debug\moc_usbdevice.cpp
OBJECTS       = debug/main.o \
		debug/usbmonitor.o \
		debug/usbdevice.o \
		debug/qrc_qml.o \
		debug/moc_usbmonitor.o \
		debug/moc_usbdevice.o

DIST          =  ..\..\usbmonitor.h \
		..\..\usbdevice.h ..\..\main.cpp \
		..\..\usbmonitor.cpp \
		..\..\usbdevice.cpp
QMAKE_TARGET  = UsbDetect
DESTDIR        = debug\ #avoid trailing-slash linebreak
TARGET         = UsbDetect.exe
DESTDIR_TARGET = debug\UsbDetect.exe

####### Build rules

first: all
all: Makefile.Debug  debug/UsbDetect.exe

debug/UsbDetect.exe: C:/Qt/6.9.0/mingw_64/lib/libQt6Quick.a C:/Qt/6.9.0/mingw_64/lib/libQt6OpenGL.a C:/Qt/6.9.0/mingw_64/lib/libQt6Gui.a C:/Qt/6.9.0/mingw_64/lib/libQt6QmlMeta.a C:/Qt/6.9.0/mingw_64/lib/libQt6QmlModels.a C:/Qt/6.9.0/mingw_64/lib/libQt6QmlWorkerScript.a C:/Qt/6.9.0/mingw_64/lib/libQt6Qml.a C:/Qt/6.9.0/mingw_64/lib/libQt6Network.a C:/Qt/6.9.0/mingw_64/lib/libQt6Core.a C:/Qt/6.9.0/mingw_64/lib/libQt6EntryPoint.a $(OBJECTS) 
	$(LINKER) $(LFLAGS) -o $(DESTDIR_TARGET) $(OBJECTS) $(LIBS)

qmake: FORCE
	@$(QMAKE) -o Makefile.Debug ..\..\UsbDetect.pro -spec win32-g++ "CONFIG+=debug" "CONFIG+=qml_debug"

qmake_all: FORCE

dist:
	$(ZIP) UsbDetect.zip $(SOURCES) $(DIST) ..\..\UsbDetect.pro C:\Qt\6.9.0\mingw_64\mkspecs\features\spec_pre.prf C:\Qt\6.9.0\mingw_64\mkspecs\features\device_config.prf C:\Qt\6.9.0\mingw_64\mkspecs\common\sanitize.conf C:\Qt\6.9.0\mingw_64\mkspecs\common\gcc-base.conf C:\Qt\6.9.0\mingw_64\mkspecs\common\g++-base.conf C:\Qt\6.9.0\mingw_64\mkspecs\features\win32\windows_vulkan_sdk.prf C:\Qt\6.9.0\mingw_64\mkspecs\common\windows-vulkan.conf C:\Qt\6.9.0\mingw_64\mkspecs\common\g++-win32.conf C:\Qt\6.9.0\mingw_64\mkspecs\common\windows-desktop.conf C:\Qt\6.9.0\mingw_64\mkspecs\qconfig.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_ext_freetype.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_ext_libjpeg.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_ext_libpng.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_ext_openxr_loader.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_charts.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_charts_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_chartsqml.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_chartsqml_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_concurrent.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_concurrent_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_core.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_core_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_dbus.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_dbus_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_designer.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_designer_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_designercomponents_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_devicediscovery_support_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_entrypoint_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_example_icons_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_examples_asset_downloader_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_fb_support_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_ffmpegmediapluginimpl_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_freetype_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_gui.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_gui_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_harfbuzz_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_help.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_help_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_jpeg_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_labsanimation.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_labsanimation_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_labsfolderlistmodel.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_labsfolderlistmodel_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_labsplatform.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_labsplatform_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_labsqmlmodels.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_labsqmlmodels_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_labssettings.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_labssettings_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_labssharedimage.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_labssharedimage_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_labswavefrontmesh.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_labswavefrontmesh_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_linguist.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_multimedia.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_multimedia_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_multimediaquick_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_multimediatestlibprivate_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_multimediawidgets.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_multimediawidgets_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_network.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_network_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_opengl.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_opengl_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_openglwidgets.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_openglwidgets_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_packetprotocol_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_png_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_printsupport.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_printsupport_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_qdoccatch_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_qdoccatchconversions_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_qdoccatchgenerators_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_qml.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_qml_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlassetdownloader.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlassetdownloader_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlcompiler.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlcompiler_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlcore.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlcore_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmldebug_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmldom_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlformat_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlintegration.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlintegration_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmllocalstorage.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmllocalstorage_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlls_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlmeta.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlmeta_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlmodels.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlmodels_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlnetwork.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlnetwork_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmltest.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmltest_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmltoolingsettings_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmltyperegistrar_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlworkerscript.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlworkerscript_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlxmllistmodel.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_qmlxmllistmodel_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3d.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3d_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3dassetimport.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3dassetimport_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3dassetutils.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3dassetutils_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3deffects.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3deffects_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3dglslparser_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3dhelpers.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3dhelpers_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3dhelpersimpl.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3dhelpersimpl_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3diblbaker.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3diblbaker_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3dparticleeffects.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3dparticleeffects_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3dparticles.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3dparticles_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3druntimerender.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3druntimerender_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3dspatialaudio_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3dutils.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3dutils_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3dxr.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick3dxr_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quick_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2basic.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2basic_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2basicstyleimpl.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2basicstyleimpl_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fluentwinui3styleimpl.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fluentwinui3styleimpl_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fusion.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fusion_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fusionstyleimpl.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fusionstyleimpl_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2imagine.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2imagine_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2imaginestyleimpl.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2imaginestyleimpl_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2impl.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2impl_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2material.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2material_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2materialstyleimpl.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2materialstyleimpl_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2universal.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2universal_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2universalstyleimpl.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2universalstyleimpl_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2windowsstyleimpl.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrols2windowsstyleimpl_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickcontrolstestutilsprivate_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickdialogs2.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickdialogs2_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickdialogs2quickimpl.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickdialogs2quickimpl_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickdialogs2utils.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickdialogs2utils_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickeffects.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickeffects_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quicklayouts.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quicklayouts_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickparticles_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickshapes_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quicktemplates2.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quicktemplates2_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quicktestutilsprivate_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quicktimeline.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quicktimeline_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quicktimelineblendtrees.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quicktimelineblendtrees_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickvectorimage.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickvectorimage_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickvectorimagegenerator_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickwidgets.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_quickwidgets_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_serialbus.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_serialbus_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_serialport.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_serialport_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_shadertools.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_shadertools_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_spatialaudio.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_spatialaudio_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_sql.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_sql_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_svg.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_svg_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_svgwidgets.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_svgwidgets_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_testinternals_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_testlib.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_testlib_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_tools_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_uiplugin.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_uitools.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_uitools_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_widgets.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_widgets_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_xml.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_xml_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\modules\qt_lib_zlib_private.pri C:\Qt\6.9.0\mingw_64\mkspecs\features\qt_functions.prf C:\Qt\6.9.0\mingw_64\mkspecs\features\qt_config.prf C:\Qt\6.9.0\mingw_64\mkspecs\win32-g++\qmake.conf C:\Qt\6.9.0\mingw_64\mkspecs\features\spec_post.prf .qmake.stash C:\Qt\6.9.0\mingw_64\mkspecs\features\exclusive_builds.prf C:\Qt\6.9.0\mingw_64\mkspecs\features\toolchain.prf C:\Qt\6.9.0\mingw_64\mkspecs\features\default_pre.prf C:\Qt\6.9.0\mingw_64\mkspecs\features\win32\default_pre.prf C:\Qt\6.9.0\mingw_64\mkspecs\features\resolve_config.prf C:\Qt\6.9.0\mingw_64\mkspecs\features\exclusive_builds_post.prf C:\Qt\6.9.0\mingw_64\mkspecs\features\default_post.prf C:\Qt\6.9.0\mingw_64\mkspecs\features\build_pass.prf C:\Qt\6.9.0\mingw_64\mkspecs\features\qml_debug.prf C:\Qt\6.9.0\mingw_64\mkspecs\features\precompile_header.prf C:\Qt\6.9.0\mingw_64\mkspecs\features\warn_on.prf C:\Qt\6.9.0\mingw_64\mkspecs\features\permissions.prf C:\Qt\6.9.0\mingw_64\mkspecs\features\qt.prf C:\Qt\6.9.0\mingw_64\mkspecs\features\resources_functions.prf C:\Qt\6.9.0\mingw_64\mkspecs\features\resources.prf C:\Qt\6.9.0\mingw_64\mkspecs\features\moc.prf C:\Qt\6.9.0\mingw_64\mkspecs\features\win32\opengl.prf C:\Qt\6.9.0\mingw_64\mkspecs\features\qmake_use.prf C:\Qt\6.9.0\mingw_64\mkspecs\features\file_copies.prf C:\Qt\6.9.0\mingw_64\mkspecs\features\win32\windows.prf C:\Qt\6.9.0\mingw_64\mkspecs\features\testcase_targets.prf C:\Qt\6.9.0\mingw_64\mkspecs\features\exceptions.prf C:\Qt\6.9.0\mingw_64\mkspecs\features\yacc.prf C:\Qt\6.9.0\mingw_64\mkspecs\features\lex.prf ..\..\UsbDetect.pro ..\..\qml.qrc C:\Qt\6.9.0\mingw_64\lib\Qt6Quick.prl C:\Qt\6.9.0\mingw_64\lib\Qt6OpenGL.prl C:\Qt\6.9.0\mingw_64\lib\Qt6Gui.prl C:\Qt\6.9.0\mingw_64\lib\Qt6QmlMeta.prl C:\Qt\6.9.0\mingw_64\lib\Qt6QmlModels.prl C:\Qt\6.9.0\mingw_64\lib\Qt6QmlWorkerScript.prl C:\Qt\6.9.0\mingw_64\lib\Qt6Qml.prl C:\Qt\6.9.0\mingw_64\lib\Qt6Network.prl C:\Qt\6.9.0\mingw_64\lib\Qt6Core.prl C:\Qt\6.9.0\mingw_64\lib\Qt6EntryPoint.prl   ..\..\qml.qrc C:\Qt\6.9.0\mingw_64\mkspecs\features\data\dummy.cpp ..\..\usbmonitor.h ..\..\usbdevice.h  ..\..\main.cpp ..\..\usbmonitor.cpp ..\..\usbdevice.cpp    

clean: compiler_clean 
	-$(DEL_FILE) debug\main.o debug\usbmonitor.o debug\usbdevice.o debug\qrc_qml.o debug\moc_usbmonitor.o debug\moc_usbdevice.o

distclean: clean 
	-$(DEL_FILE) .qmake.stash
	-$(DEL_FILE) $(DESTDIR_TARGET)
	-$(DEL_FILE) Makefile.Debug

mocclean: compiler_moc_header_clean compiler_moc_objc_header_clean compiler_moc_source_clean

mocables: compiler_moc_header_make_all compiler_moc_objc_header_make_all compiler_moc_source_make_all

check: first

benchmark: first

compiler_no_pch_compiler_make_all:
compiler_no_pch_compiler_clean:
compiler_rcc_make_all: debug/qrc_qml.cpp
compiler_rcc_clean:
	-$(DEL_FILE) debug\qrc_qml.cpp
debug/qrc_qml.cpp: ../../qml.qrc \
		C:/Qt/6.9.0/mingw_64/bin/rcc.exe \
		../../UsbDeviceDelegate.qml \
		../../main.qml
	C:\Qt\6.9.0\mingw_64\bin\rcc.exe -name qml --no-zstd ..\..\qml.qrc -o debug\qrc_qml.cpp

compiler_moc_predefs_make_all: debug/moc_predefs.h
compiler_moc_predefs_clean:
	-$(DEL_FILE) debug\moc_predefs.h
debug/moc_predefs.h: C:/Qt/6.9.0/mingw_64/mkspecs/features/data/dummy.cpp
	g++ -fno-keep-inline-dllexport -g -std=gnu++1z -Wall -Wextra -Wextra -dM -E -o debug\moc_predefs.h C:\Qt\6.9.0\mingw_64\mkspecs\features\data\dummy.cpp

compiler_moc_header_make_all: debug/moc_usbmonitor.cpp debug/moc_usbdevice.cpp
compiler_moc_header_clean:
	-$(DEL_FILE) debug\moc_usbmonitor.cpp debug\moc_usbdevice.cpp
debug/moc_usbmonitor.cpp: ../../usbmonitor.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/QObject \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qobject.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qobjectdefs.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qnamespace.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qglobal.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtcoreglobal.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtversionchecks.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtconfiginclude.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qconfig.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtcore-config.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtconfigmacros.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcompilerdetection.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qprocessordetection.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qsystemdetection.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtcoreexports.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtclasshelpermacros.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qassert.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtnoop.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtypes.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtversion.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtypeinfo.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcontainerfwd.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qsysinfo.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qlogging.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qflags.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcompare_impl.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qatomic.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qbasicatomic.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qatomic_cxx11.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qgenericatomic.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qyieldcpu.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qconstructormacros.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qdarwinhelpers.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qexceptionhandling.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qforeach.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qttypetraits.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qfunctionpointer.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qglobalstatic.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qmalloc.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qminmax.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qnumeric.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qoverload.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qswap.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtenvironmentvariables.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtresource.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qttranslation.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qversiontagging.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcompare.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstdlibdetection.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcomparehelpers.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/q20type_traits.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtmetamacros.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qobjectdefs_impl.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstring.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qchar.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstringview.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qbytearray.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qrefcount.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qarraydata.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qpair.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qarraydatapointer.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qarraydataops.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcontainertools_impl.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qxptype_traits.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/q20functional.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/q20memory.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/q17memory.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qbytearrayview.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstringfwd.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstringliteral.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstringalgorithms.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qlatin1stringview.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qanystringview.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qutf8stringview.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstringtokenizer.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstringbuilder.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstringconverter.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstringconverter_base.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qlist.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qhashfunctions.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qiterator.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qbytearraylist.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstringlist.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qalgorithms.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstringmatcher.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcoreevent.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qbasictimer.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qeventloop.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qdeadlinetimer.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qelapsedtimer.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qmetatype.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qdatastream.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qscopedpointer.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qiodevicebase.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qfloat16.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qmath.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtformat_impl.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qiterable.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qmetacontainer.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcontainerinfo.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtaggedpointer.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qscopeguard.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qobject_impl.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qbindingstorage.h \
		C:/Qt/6.9.0/mingw_64/include/QtQml/QQmlListProperty \
		C:/Qt/6.9.0/mingw_64/include/QtQml/qqmllist.h \
		C:/Qt/6.9.0/mingw_64/include/QtQml/qtqmlglobal.h \
		C:/Qt/6.9.0/mingw_64/include/QtQml/qtqml-config.h \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/qtnetworkglobal.h \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/qtnetwork-config.h \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/qtnetworkexports.h \
		C:/Qt/6.9.0/mingw_64/include/QtQml/qtqmlexports.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qvariant.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qdebug.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtextstream.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcontiguouscache.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qsharedpointer.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qshareddata.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qsharedpointer_impl.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qmap.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qshareddata_impl.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qset.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qhash.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qvarlengtharray.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/q23utility.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/q20utility.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/QTimer \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtimer.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/QList \
		../../usbdevice.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/QString \
		debug/moc_predefs.h \
		C:/Qt/6.9.0/mingw_64/bin/moc.exe
	C:\Qt\6.9.0\mingw_64\bin\moc.exe $(DEFINES) --include D:/app/QT/UsbDetect/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/debug/moc_predefs.h -IC:/Qt/6.9.0/mingw_64/mkspecs/win32-g++ -ID:/app/QT/UsbDetect -IC:/Qt/6.9.0/mingw_64/include -IC:/Qt/6.9.0/mingw_64/include/QtQuick -IC:/Qt/6.9.0/mingw_64/include/QtOpenGL -IC:/Qt/6.9.0/mingw_64/include/QtGui -IC:/Qt/6.9.0/mingw_64/include/QtQmlMeta -IC:/Qt/6.9.0/mingw_64/include/QtQmlModels -IC:/Qt/6.9.0/mingw_64/include/QtQmlWorkerScript -IC:/Qt/6.9.0/mingw_64/include/QtQml -IC:/Qt/6.9.0/mingw_64/include/QtQmlIntegration -IC:/Qt/6.9.0/mingw_64/include/QtNetwork -IC:/Qt/6.9.0/mingw_64/include/QtCore -I. -IC:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++ -IC:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32 -IC:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward -IC:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include -IC:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed -IC:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include ..\..\usbmonitor.h -o debug\moc_usbmonitor.cpp

debug/moc_usbdevice.cpp: ../../usbdevice.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/QObject \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qobject.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qobjectdefs.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qnamespace.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qglobal.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtcoreglobal.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtversionchecks.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtconfiginclude.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qconfig.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtcore-config.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtconfigmacros.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcompilerdetection.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qprocessordetection.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qsystemdetection.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtcoreexports.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtclasshelpermacros.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qassert.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtnoop.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtypes.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtversion.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtypeinfo.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcontainerfwd.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qsysinfo.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qlogging.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qflags.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcompare_impl.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qatomic.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qbasicatomic.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qatomic_cxx11.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qgenericatomic.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qyieldcpu.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qconstructormacros.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qdarwinhelpers.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qexceptionhandling.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qforeach.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qttypetraits.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qfunctionpointer.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qglobalstatic.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qmalloc.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qminmax.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qnumeric.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qoverload.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qswap.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtenvironmentvariables.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtresource.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qttranslation.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qversiontagging.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcompare.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstdlibdetection.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcomparehelpers.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/q20type_traits.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtmetamacros.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qobjectdefs_impl.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstring.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qchar.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstringview.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qbytearray.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qrefcount.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qarraydata.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qpair.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qarraydatapointer.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qarraydataops.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcontainertools_impl.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qxptype_traits.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/q20functional.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/q20memory.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/q17memory.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qbytearrayview.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstringfwd.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstringliteral.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstringalgorithms.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qlatin1stringview.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qanystringview.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qutf8stringview.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstringtokenizer.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstringbuilder.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstringconverter.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstringconverter_base.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qlist.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qhashfunctions.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qiterator.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qbytearraylist.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstringlist.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qalgorithms.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstringmatcher.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcoreevent.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qbasictimer.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qeventloop.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qdeadlinetimer.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qelapsedtimer.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qmetatype.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qdatastream.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qscopedpointer.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qiodevicebase.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qfloat16.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qmath.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtformat_impl.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qiterable.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qmetacontainer.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcontainerinfo.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtaggedpointer.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qscopeguard.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qobject_impl.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qbindingstorage.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/QString \
		debug/moc_predefs.h \
		C:/Qt/6.9.0/mingw_64/bin/moc.exe
	C:\Qt\6.9.0\mingw_64\bin\moc.exe $(DEFINES) --include D:/app/QT/UsbDetect/build/Desktop_Qt_6_9_0_MinGW_64_bit-Debug/debug/moc_predefs.h -IC:/Qt/6.9.0/mingw_64/mkspecs/win32-g++ -ID:/app/QT/UsbDetect -IC:/Qt/6.9.0/mingw_64/include -IC:/Qt/6.9.0/mingw_64/include/QtQuick -IC:/Qt/6.9.0/mingw_64/include/QtOpenGL -IC:/Qt/6.9.0/mingw_64/include/QtGui -IC:/Qt/6.9.0/mingw_64/include/QtQmlMeta -IC:/Qt/6.9.0/mingw_64/include/QtQmlModels -IC:/Qt/6.9.0/mingw_64/include/QtQmlWorkerScript -IC:/Qt/6.9.0/mingw_64/include/QtQml -IC:/Qt/6.9.0/mingw_64/include/QtQmlIntegration -IC:/Qt/6.9.0/mingw_64/include/QtNetwork -IC:/Qt/6.9.0/mingw_64/include/QtCore -I. -IC:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++ -IC:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/x86_64-w64-mingw32 -IC:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include/c++/backward -IC:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include -IC:/Qt/Tools/mingw1310_64/lib/gcc/x86_64-w64-mingw32/13.1.0/include-fixed -IC:/Qt/Tools/mingw1310_64/x86_64-w64-mingw32/include ..\..\usbdevice.h -o debug\moc_usbdevice.cpp

compiler_moc_objc_header_make_all:
compiler_moc_objc_header_clean:
compiler_moc_source_make_all:
compiler_moc_source_clean:
compiler_yacc_decl_make_all:
compiler_yacc_decl_clean:
compiler_yacc_impl_make_all:
compiler_yacc_impl_clean:
compiler_lex_make_all:
compiler_lex_clean:
compiler_clean: compiler_rcc_clean compiler_moc_predefs_clean compiler_moc_header_clean 



####### Compile

debug/main.o: ../../main.cpp C:/Qt/6.9.0/mingw_64/include/QtGui/QGuiApplication \
		C:/Qt/6.9.0/mingw_64/include/QtGui/qguiapplication.h \
		C:/Qt/6.9.0/mingw_64/include/QtGui/qtguiglobal.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qglobal.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtcoreglobal.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtversionchecks.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtconfiginclude.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qconfig.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtcore-config.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtconfigmacros.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcompilerdetection.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qprocessordetection.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qsystemdetection.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtcoreexports.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtclasshelpermacros.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qassert.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtnoop.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtypes.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtversion.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtypeinfo.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcontainerfwd.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qsysinfo.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qlogging.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qflags.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcompare_impl.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qatomic.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qbasicatomic.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qatomic_cxx11.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qgenericatomic.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qyieldcpu.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qconstructormacros.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qdarwinhelpers.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qexceptionhandling.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qforeach.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qttypetraits.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qfunctionpointer.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qglobalstatic.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qmalloc.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qminmax.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qnumeric.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qoverload.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qswap.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtenvironmentvariables.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtresource.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qttranslation.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qversiontagging.h \
		C:/Qt/6.9.0/mingw_64/include/QtGui/qtgui-config.h \
		C:/Qt/6.9.0/mingw_64/include/QtGui/qtguiexports.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcoreapplication.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstring.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qchar.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcompare.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstdlibdetection.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcomparehelpers.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/q20type_traits.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstringview.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qbytearray.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qrefcount.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qnamespace.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtmetamacros.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qarraydata.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qpair.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qarraydatapointer.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qarraydataops.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcontainertools_impl.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qxptype_traits.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/q20functional.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/q20memory.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/q17memory.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qbytearrayview.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstringfwd.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstringliteral.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstringalgorithms.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qlatin1stringview.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qanystringview.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qutf8stringview.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstringtokenizer.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstringbuilder.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstringconverter.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstringconverter_base.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcoreevent.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qbasictimer.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qobject.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qobjectdefs.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qobjectdefs_impl.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qlist.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qhashfunctions.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qiterator.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qbytearraylist.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstringlist.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qalgorithms.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstringmatcher.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qscopedpointer.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qmetatype.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qdatastream.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qiodevicebase.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qfloat16.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qmath.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtformat_impl.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qiterable.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qmetacontainer.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcontainerinfo.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtaggedpointer.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qscopeguard.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qobject_impl.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qbindingstorage.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qeventloop.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qdeadlinetimer.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qelapsedtimer.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qnativeinterface.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcoreapplication_platform.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qfuture.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qfutureinterface.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qmutex.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtsan_impl.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qresultstore.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qmap.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qshareddata.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qshareddata_impl.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qfuture_impl.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qthreadpool.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qthread.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qrunnable.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qexception.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qpromise.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qvariant.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qdebug.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtextstream.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcontiguouscache.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qsharedpointer.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qsharedpointer_impl.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qset.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qhash.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qvarlengtharray.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/q23utility.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/q20utility.h \
		C:/Qt/6.9.0/mingw_64/include/QtGui/qwindowdefs.h \
		C:/Qt/6.9.0/mingw_64/include/QtGui/qwindowdefs_win.h \
		C:/Qt/6.9.0/mingw_64/include/QtGui/qinputmethod.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qlocale.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qpoint.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qsize.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qmargins.h \
		C:/Qt/6.9.0/mingw_64/include/QtGui/qguiapplication_platform.h \
		C:/Qt/6.9.0/mingw_64/include/QtQml/QQmlApplicationEngine \
		C:/Qt/6.9.0/mingw_64/include/QtQml/qqmlapplicationengine.h \
		C:/Qt/6.9.0/mingw_64/include/QtQml/qqmlengine.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qurl.h \
		C:/Qt/6.9.0/mingw_64/include/QtQml/qjsengine.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtimezone.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qdatetime.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcalendar.h \
		C:/Qt/6.9.0/mingw_64/include/QtQml/qjsvalue.h \
		C:/Qt/6.9.0/mingw_64/include/QtQml/qtqmlglobal.h \
		C:/Qt/6.9.0/mingw_64/include/QtQml/qtqml-config.h \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/qtnetworkglobal.h \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/qtnetwork-config.h \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/qtnetworkexports.h \
		C:/Qt/6.9.0/mingw_64/include/QtQml/qtqmlexports.h \
		C:/Qt/6.9.0/mingw_64/include/QtQml/qjsmanagedvalue.h \
		C:/Qt/6.9.0/mingw_64/include/QtQml/qjsprimitivevalue.h \
		C:/Qt/6.9.0/mingw_64/include/QtQml/qjsnumbercoercion.h \
		C:/Qt/6.9.0/mingw_64/include/QtQml/qqmldebug.h \
		C:/Qt/6.9.0/mingw_64/include/QtQml/qqml.h \
		C:/Qt/6.9.0/mingw_64/include/QtQml/qqmlprivate.h \
		C:/Qt/6.9.0/mingw_64/include/QtQml/qqmllist.h \
		C:/Qt/6.9.0/mingw_64/include/QtQml/qqmlparserstatus.h \
		C:/Qt/6.9.0/mingw_64/include/QtQml/qqmlpropertyvaluesource.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qmetaobject.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qpointer.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qversionnumber.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qspan.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/q20iterator.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtyperevision.h \
		C:/Qt/6.9.0/mingw_64/include/QtQml/qqmlregistration.h \
		C:/Qt/6.9.0/mingw_64/include/QtQmlIntegration/qqmlintegration.h \
		C:/Qt/6.9.0/mingw_64/include/QtQml/qqmlerror.h \
		C:/Qt/6.9.0/mingw_64/include/QtQml/qqmlabstracturlinterceptor.h \
		C:/Qt/6.9.0/mingw_64/include/QtQml/QQmlContext \
		C:/Qt/6.9.0/mingw_64/include/QtQml/qqmlcontext.h \
		C:/Qt/6.9.0/mingw_64/include/QtQml/QtQml \
		C:/Qt/6.9.0/mingw_64/include/QtQml/QtQmlDepends \
		C:/Qt/6.9.0/mingw_64/include/QtCore/QtCore \
		C:/Qt/6.9.0/mingw_64/include/QtCore/QtCoreDepends \
		C:/Qt/6.9.0/mingw_64/include/QtCore/q20algorithm.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/q20chrono.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/q20map.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/q20vector.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/q23functional.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/q26numeric.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qabstractanimation.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qabstractitemmodel.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qabstractnativeeventfilter.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qabstractproxymodel.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qanimationgroup.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qapplicationstatic.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/QMutex \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qassociativeiterable.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qatomicscopedvaluerollback.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qbitarray.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qbuffer.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qiodevice.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qbytearraymatcher.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcache.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcborarray.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcborvalue.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcborcommon.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qregularexpression.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/quuid.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qendian.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcbormap.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcborstream.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcborstreamreader.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcborstreamwriter.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qchronotimer.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qproperty.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qpropertyprivate.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtimer.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcollator.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcommandlineoption.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcommandlineparser.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qconcatenatetablesproxymodel.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcryptographichash.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qdir.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qdirlisting.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qfiledevice.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qfile.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qfileinfo.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qdiriterator.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qeasingcurve.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qfactoryinterface.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qfileselector.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/QObject \
		C:/Qt/6.9.0/mingw_64/include/QtCore/QStringList \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qfilesystemwatcher.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qfuturesynchronizer.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qfuturewatcher.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qidentityproxymodel.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qitemselectionmodel.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qjsonarray.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qjsonvalue.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qjsondocument.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qjsonparseerror.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qjsonobject.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qlatin1stringmatcher.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qlibrary.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qlibraryinfo.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qline.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qlockfile.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qloggingcategory.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qmessageauthenticationcode.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qmimedata.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qmimedatabase.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qmimetype.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qobjectcleanuphandler.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qoperatingsystemversion.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qparallelanimationgroup.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qpauseanimation.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qpermissions.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qplugin.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qpluginloader.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qprocess.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qpropertyanimation.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qvariantanimation.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qqueue.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qrandom.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qreadwritelock.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qrect.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qresource.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qsavefile.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qscopedvaluerollback.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qsemaphore.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qsequentialanimationgroup.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qsequentialiterable.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qsettings.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qsharedmemory.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtipccommon.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qsignalmapper.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qsimd.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qsocketnotifier.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qsortfilterproxymodel.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstack.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstandardpaths.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstaticlatin1stringmatcher.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstorageinfo.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstringlistmodel.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qsystemsemaphore.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtcoreversion.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtemporarydir.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtemporaryfile.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtextboundaryfinder.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qthreadstorage.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtimeline.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtmocconstants.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtranslator.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtransposeproxymodel.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtsymbolmacros.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qurlquery.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qvarianthash.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/QHash \
		C:/Qt/6.9.0/mingw_64/include/QtCore/QVariant \
		C:/Qt/6.9.0/mingw_64/include/QtCore/QString \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qvariantlist.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/QList \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qvariantmap.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/QMap \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qvector.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qwaitcondition.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/QDeadlineTimer \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qwineventnotifier.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qxmlstream.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qxpfunctional.h \
		C:/Qt/6.9.0/mingw_64/include/QtQmlIntegration/QtQmlIntegration \
		C:/Qt/6.9.0/mingw_64/include/QtQmlIntegration/QtQmlIntegrationDepends \
		C:/Qt/6.9.0/mingw_64/include/QtQmlIntegration/qtqmlintegrationversion.h \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/QtNetwork \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/QtNetworkDepends \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/qabstractnetworkcache.h \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/qnetworkrequest.h \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/qhttpheaders.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/QSharedDataPointer \
		C:/Qt/6.9.0/mingw_64/include/QtCore/QUrl \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/qabstractsocket.h \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/qhostaddress.h \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/qauthenticator.h \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/qdnslookup.h \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/qdtls.h \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/qsslsocket.h \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/qtcpsocket.h \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/qsslerror.h \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/qsslcertificate.h \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/qssl.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/QFlags \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/qformdatabuilder.h \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/qhttpmultipart.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/QByteArray \
		C:/Qt/6.9.0/mingw_64/include/QtCore/QIODevice \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/QNetworkRequest \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/qhostinfo.h \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/qhstspolicy.h \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/qhttp1configuration.h \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/qhttp2configuration.h \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/qlocalserver.h \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/qlocalsocket.h \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/qnetworkaccessmanager.h \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/QSslConfiguration \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/qsslconfiguration.h \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/QSslPreSharedKeyAuthenticator \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/qsslpresharedkeyauthenticator.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/QMetaType \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/qnetworkcookie.h \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/qnetworkcookiejar.h \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/qnetworkdatagram.h \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/qnetworkdiskcache.h \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/qnetworkinformation.h \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/qnetworkinterface.h \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/qnetworkproxy.h \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/qnetworkreply.h \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/QNetworkAccessManager \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/qnetworkrequestfactory.h \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/qocspresponse.h \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/qpassworddigestor.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/QCryptographicHash \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/qrestaccessmanager.h \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/qrestreply.h \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/qsslcertificateextension.h \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/qsslcipher.h \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/qssldiffiehellmanparameters.h \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/qsslellipticcurve.h \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/qsslkey.h \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/qsslserver.h \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/QTcpServer \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/qtcpserver.h \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/QSslError \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/QSslSocket \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/qtnetworkversion.h \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/qudpsocket.h \
		C:/Qt/6.9.0/mingw_64/include/QtQml/qtqmlcompilerglobal.h \
		C:/Qt/6.9.0/mingw_64/include/QtQml/qjslist.h \
		C:/Qt/6.9.0/mingw_64/include/QtQml/qjsvalueiterator.h \
		C:/Qt/6.9.0/mingw_64/include/QtQml/qqmlcomponent.h \
		C:/Qt/6.9.0/mingw_64/include/QtQml/qqmlexpression.h \
		C:/Qt/6.9.0/mingw_64/include/QtQml/qqmlscriptstring.h \
		C:/Qt/6.9.0/mingw_64/include/QtQml/qqmlextensioninterface.h \
		C:/Qt/6.9.0/mingw_64/include/QtQml/qqmlextensionplugin.h \
		C:/Qt/6.9.0/mingw_64/include/QtQml/qqmlfile.h \
		C:/Qt/6.9.0/mingw_64/include/QtQml/qqmlfileselector.h \
		C:/Qt/6.9.0/mingw_64/include/QtQml/QQmlEngine \
		C:/Qt/6.9.0/mingw_64/include/QtQml/qqmlincubator.h \
		C:/Qt/6.9.0/mingw_64/include/QtQml/qqmlinfo.h \
		C:/Qt/6.9.0/mingw_64/include/QtQml/qqmlmoduleregistration.h \
		C:/Qt/6.9.0/mingw_64/include/QtQml/qqmlnetworkaccessmanagerfactory.h \
		C:/Qt/6.9.0/mingw_64/include/QtQml/qqmlproperty.h \
		C:/Qt/6.9.0/mingw_64/include/QtQml/qqmlpropertymap.h \
		C:/Qt/6.9.0/mingw_64/include/QtQml/qtqmlversion.h \
		../../usbmonitor.h \
		C:/Qt/6.9.0/mingw_64/include/QtQml/QQmlListProperty \
		C:/Qt/6.9.0/mingw_64/include/QtCore/QTimer \
		../../usbdevice.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\main.o ..\..\main.cpp

debug/usbmonitor.o: ../../usbmonitor.cpp ../../usbmonitor.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/QObject \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qobject.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qobjectdefs.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qnamespace.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qglobal.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtcoreglobal.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtversionchecks.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtconfiginclude.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qconfig.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtcore-config.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtconfigmacros.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcompilerdetection.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qprocessordetection.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qsystemdetection.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtcoreexports.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtclasshelpermacros.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qassert.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtnoop.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtypes.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtversion.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtypeinfo.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcontainerfwd.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qsysinfo.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qlogging.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qflags.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcompare_impl.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qatomic.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qbasicatomic.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qatomic_cxx11.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qgenericatomic.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qyieldcpu.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qconstructormacros.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qdarwinhelpers.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qexceptionhandling.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qforeach.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qttypetraits.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qfunctionpointer.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qglobalstatic.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qmalloc.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qminmax.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qnumeric.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qoverload.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qswap.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtenvironmentvariables.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtresource.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qttranslation.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qversiontagging.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcompare.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstdlibdetection.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcomparehelpers.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/q20type_traits.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtmetamacros.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qobjectdefs_impl.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstring.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qchar.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstringview.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qbytearray.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qrefcount.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qarraydata.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qpair.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qarraydatapointer.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qarraydataops.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcontainertools_impl.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qxptype_traits.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/q20functional.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/q20memory.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/q17memory.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qbytearrayview.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstringfwd.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstringliteral.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstringalgorithms.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qlatin1stringview.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qanystringview.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qutf8stringview.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstringtokenizer.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstringbuilder.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstringconverter.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstringconverter_base.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qlist.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qhashfunctions.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qiterator.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qbytearraylist.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstringlist.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qalgorithms.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstringmatcher.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcoreevent.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qbasictimer.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qeventloop.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qdeadlinetimer.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qelapsedtimer.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qmetatype.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qdatastream.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qscopedpointer.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qiodevicebase.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qfloat16.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qmath.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtformat_impl.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qiterable.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qmetacontainer.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcontainerinfo.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtaggedpointer.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qscopeguard.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qobject_impl.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qbindingstorage.h \
		C:/Qt/6.9.0/mingw_64/include/QtQml/QQmlListProperty \
		C:/Qt/6.9.0/mingw_64/include/QtQml/qqmllist.h \
		C:/Qt/6.9.0/mingw_64/include/QtQml/qtqmlglobal.h \
		C:/Qt/6.9.0/mingw_64/include/QtQml/qtqml-config.h \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/qtnetworkglobal.h \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/qtnetwork-config.h \
		C:/Qt/6.9.0/mingw_64/include/QtNetwork/qtnetworkexports.h \
		C:/Qt/6.9.0/mingw_64/include/QtQml/qtqmlexports.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qvariant.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qdebug.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtextstream.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcontiguouscache.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qsharedpointer.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qshareddata.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qsharedpointer_impl.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qmap.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qshareddata_impl.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qset.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qhash.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qvarlengtharray.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/q23utility.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/q20utility.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/QTimer \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtimer.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/QList \
		../../usbdevice.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/QString \
		C:/Qt/6.9.0/mingw_64/include/QtCore/QDebug \
		C:/Qt/6.9.0/mingw_64/include/QtCore/QRegularExpression \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qregularexpression.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\usbmonitor.o ..\..\usbmonitor.cpp

debug/usbdevice.o: ../../usbdevice.cpp ../../usbdevice.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/QObject \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qobject.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qobjectdefs.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qnamespace.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qglobal.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtcoreglobal.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtversionchecks.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtconfiginclude.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qconfig.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtcore-config.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtconfigmacros.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcompilerdetection.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qprocessordetection.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qsystemdetection.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtcoreexports.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtclasshelpermacros.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qassert.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtnoop.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtypes.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtversion.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtypeinfo.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcontainerfwd.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qsysinfo.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qlogging.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qflags.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcompare_impl.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qatomic.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qbasicatomic.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qatomic_cxx11.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qgenericatomic.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qyieldcpu.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qconstructormacros.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qdarwinhelpers.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qexceptionhandling.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qforeach.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qttypetraits.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qfunctionpointer.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qglobalstatic.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qmalloc.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qminmax.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qnumeric.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qoverload.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qswap.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtenvironmentvariables.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtresource.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qttranslation.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qversiontagging.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcompare.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstdlibdetection.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcomparehelpers.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/q20type_traits.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtmetamacros.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qobjectdefs_impl.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstring.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qchar.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstringview.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qbytearray.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qrefcount.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qarraydata.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qpair.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qarraydatapointer.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qarraydataops.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcontainertools_impl.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qxptype_traits.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/q20functional.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/q20memory.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/q17memory.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qbytearrayview.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstringfwd.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstringliteral.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstringalgorithms.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qlatin1stringview.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qanystringview.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qutf8stringview.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstringtokenizer.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstringbuilder.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstringconverter.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstringconverter_base.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qlist.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qhashfunctions.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qiterator.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qbytearraylist.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstringlist.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qalgorithms.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qstringmatcher.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcoreevent.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qbasictimer.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qeventloop.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qdeadlinetimer.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qelapsedtimer.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qmetatype.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qdatastream.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qscopedpointer.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qiodevicebase.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qfloat16.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qmath.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtformat_impl.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qiterable.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qmetacontainer.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qcontainerinfo.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qtaggedpointer.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qscopeguard.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qobject_impl.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/qbindingstorage.h \
		C:/Qt/6.9.0/mingw_64/include/QtCore/QString
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\usbdevice.o ..\..\usbdevice.cpp

debug/qrc_qml.o: debug/qrc_qml.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\qrc_qml.o debug\qrc_qml.cpp

debug/moc_usbmonitor.o: debug/moc_usbmonitor.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\moc_usbmonitor.o debug\moc_usbmonitor.cpp

debug/moc_usbdevice.o: debug/moc_usbdevice.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\moc_usbdevice.o debug\moc_usbdevice.cpp

####### Install

install:  FORCE

uninstall:  FORCE

FORCE:

.SUFFIXES:

