# Qt Creator 使用指南

## 在Qt Creator中打开项目

### 步骤1：打开项目文件
1. 启动Qt Creator
2. 选择 `File` -> `Open File or Project...`
3. 浏览到项目目录，选择 `UsbDetect.pro` 文件
4. 点击 `Open`

### 步骤2：配置构建套件（Kit）
1. Qt Creator会自动检测可用的构建套件
2. 选择合适的Kit（建议选择Qt 6.8或更高版本）
3. 点击 `Configure Project`

### 步骤3：检查项目配置
确保以下设置正确：
- **Qt版本**: Qt 6.8或更高
- **编译器**: 
  - Windows: MSVC 2019/2022 或 MinGW
  - Linux: GCC
- **构建模式**: Debug 或 Release

## 构建和运行

### 构建项目
- 快捷键: `Ctrl+B` (Windows/Linux)
- 菜单: `Build` -> `Build Project "UsbDetect"`

### 运行项目
- 快捷键: `Ctrl+R` (Windows/Linux)
- 菜单: `Build` -> `Run`

### 调试项目
- 快捷键: `F5`
- 菜单: `Debug` -> `Start Debugging`

## 项目文件说明

### UsbDetect.pro
这是Qt项目文件，包含：
- 项目配置
- 源文件列表
- 头文件列表
- 资源文件引用
- 平台特定的库链接

### qml.qrc
QML资源文件，包含：
- main.qml
- UsbDeviceDelegate.qml

## 常见问题解决

### 问题1：找不到Qt模块
**错误信息**: `Project ERROR: Unknown module(s) in QT: quick`

**解决方案**:
1. 确保安装了Qt Quick模块
2. 在Qt Maintenance Tool中添加Qt Quick组件

### 问题2：找不到setupapi库（Windows）
**错误信息**: `cannot find -lsetupapi`

**解决方案**:
1. 确保安装了Windows SDK
2. 使用MSVC编译器而不是MinGW

### 问题3：找不到libudev（Linux）
**错误信息**: `Package libudev was not found`

**解决方案**:
```bash
# Ubuntu/Debian
sudo apt-get install libudev-dev

# CentOS/RHEL/Fedora
sudo yum install libudev-devel
# 或
sudo dnf install libudev-devel
```

### 问题4：QML文件无法加载
**错误信息**: `qrc:/main.qml: File not found`

**解决方案**:
1. 确保qml.qrc文件存在
2. 重新构建项目以生成资源文件

## 开发建议

### 代码编辑
- 使用Qt Creator的代码补全功能
- 利用F2快速跳转到定义
- 使用Ctrl+I自动格式化代码

### QML开发
- 使用Qt Quick Designer进行可视化设计
- 利用QML预览功能实时查看界面变化
- 使用QML调试器调试界面逻辑

### 调试技巧
- 在C++代码中使用qDebug()输出调试信息
- 在QML中使用console.log()输出调试信息
- 使用断点调试复杂逻辑

## 部署

### Windows部署
1. 构建Release版本
2. 使用windeployqt工具部署：
   ```cmd
   windeployqt.exe UsbDetect.exe
   ```

### Linux部署
1. 构建Release版本
2. 确保目标系统安装了Qt运行时库
3. 或使用静态链接构建独立可执行文件

## 扩展开发

### 添加新功能
1. 在usbmonitor.h中声明新方法
2. 在usbmonitor.cpp中实现
3. 在QML中调用新功能

### 修改界面
1. 编辑main.qml或UsbDeviceDelegate.qml
2. 使用Qt Quick Designer进行可视化编辑
3. 实时预览界面变化
