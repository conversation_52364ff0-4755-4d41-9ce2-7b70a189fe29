# USB Device Monitor

一个基于Qt 6.8的USB设备监控应用程序，使用QML实现用户界面。

## 功能特性

- 实时监控USB设备的插入和移除
- 显示USB设备的详细信息：
  - VID (Vendor ID) / PID (Product ID)
  - 设备状态（已连接/已断开）
  - 设备类别/功能
  - 设备厂商信息
  - 产品名称
  - 设备路径

## 系统要求

- Qt 6.8 或更高版本
- CMake 3.16 或更高版本
- C++17 编译器

### 平台特定要求

**Windows:**
- Windows SDK (包含 setupapi.lib)

**Linux:**
- libudev 开发库
  ```bash
  # Ubuntu/Debian
  sudo apt-get install libudev-dev
  
  # CentOS/RHEL/Fedora
  sudo yum install libudev-devel
  # 或
  sudo dnf install libudev-devel
  ```

## 构建说明

### 方法一：使用Qt Creator（推荐）

1. 打开Qt Creator
2. 选择 "Open Project"
3. 选择 `UsbDetect.pro` 文件
4. 配置构建套件（Kit）
5. 点击运行按钮或按 Ctrl+R

### 方法二：使用qmake构建脚本

**Windows:**
```cmd
build_qmake.bat
```

**Linux:**
```bash
./build_qmake.sh
```

### 方法三：使用CMake构建脚本

**Windows:**
```cmd
build.bat
```

**Linux:**
```bash
./build.sh
```

### 方法四：手动构建

#### 使用qmake（推荐）
```bash
qmake UsbDetect.pro
make          # Linux
nmake         # Windows with MSVC
mingw32-make  # Windows with MinGW
```

#### 使用CMake
1. 创建构建目录：
   ```bash
   mkdir build && cd build
   ```

2. 配置CMake：
   ```bash
   cmake ..
   ```
   如果Qt不在标准路径，需要设置CMAKE_PREFIX_PATH：
   ```bash
   # Windows示例
   set CMAKE_PREFIX_PATH=C:\Qt\6.8.0\msvc2022_64

   # Linux示例
   export CMAKE_PREFIX_PATH=/opt/Qt/6.8.0/gcc_64
   ```

3. 编译项目：
   ```bash
   cmake --build . --config Release
   ```

4. 运行程序：
   ```bash
   # Windows
   .\Release\UsbDetect.exe

   # Linux
   ./UsbDetect
   ```

## 使用说明

1. 启动程序后，点击"Start Monitoring"开始监控USB设备
2. 插入或移除USB设备时，列表会自动更新
3. 点击"Refresh"手动刷新设备列表
4. 点击"Stop Monitoring"停止监控

## 项目结构

```
UsbDetect/
├── UsbDetect.pro           # Qt项目文件（Qt Creator）
├── qml.qrc                 # QML资源文件
├── CMakeLists.txt          # CMake构建配置
├── main.cpp                # 主程序入口
├── usbmonitor.h/.cpp       # USB监控器类
├── usbdevice.h/.cpp        # USB设备数据类
├── main.qml                # 主界面QML
├── UsbDeviceDelegate.qml   # 设备信息显示组件
├── build_qmake.bat/.sh     # qmake构建脚本
├── build.bat/.sh           # CMake构建脚本
├── test_basic.cpp          # 基础功能测试
├── README.md               # 项目说明
└── build/                  # 构建输出目录
```

## 技术实现

- **后端**: C++类负责USB设备检测和数据管理
- **前端**: QML实现现代化的用户界面
- **平台支持**: 
  - Windows: 使用Windows Setup API
  - Linux: 使用libudev库
- **架构**: Model-View模式，支持数据绑定和自动更新

## 注意事项

- 在Linux系统上，某些USB设备信息可能需要root权限才能完全访问
- 程序会每2秒自动检查设备变化
- 支持热插拔检测
