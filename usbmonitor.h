#ifndef USBMONITOR_H
#define USBMONITOR_H

#include <QObject>
#include <QQmlListProperty>
#include <QTimer>
#include <QList>
#include "usbdevice.h"

#ifdef _WIN32
#include <windows.h>
#include <setupapi.h>
#include <devguid.h>
#include <regstr.h>
#elif defined(__linux__)
#include <libudev.h>
#endif

class UsbMonitor : public QObject
{
    Q_OBJECT
    Q_PROPERTY(QQmlListProperty<UsbDevice> devices READ devices NOTIFY devicesChanged)

public:
    explicit UsbMonitor(QObject *parent = nullptr);
    ~UsbMonitor();

    QQmlListProperty<UsbDevice> devices();
    Q_INVOKABLE void startMonitoring();
    Q_INVOKABLE void stopMonitoring();
    Q_INVOKABLE void refreshDevices();

signals:
    void devicesChanged();
    void deviceAdded(UsbDevice *device);
    void deviceRemoved(const QString &devicePath);

private slots:
    void checkForDeviceChanges();

private:
    void scanForUsbDevices();
    QString getDeviceClass(int classCode);
    
#ifdef _WIN32
    void scanWindowsUsbDevices();
    QString getWindowsDeviceProperty(HDEVINFO deviceInfoSet, SP_DEVINFO_DATA &deviceInfoData, DWORD property);
#elif defined(__linux__)
    void scanLinuxUsbDevices();
    struct udev *m_udev;
    struct udev_monitor *m_monitor;
#endif

    QList<UsbDevice*> m_devices;
    QTimer *m_timer;
    
    // QQmlListProperty helper functions
    static qsizetype devicesCount(QQmlListProperty<UsbDevice> *list);
    static UsbDevice *deviceAt(QQmlListProperty<UsbDevice> *list, qsizetype index);
};

#endif // USBMONITOR_H
