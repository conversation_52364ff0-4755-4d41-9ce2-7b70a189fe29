#include "usbdevice.h"

UsbDevice::UsbDevice(QObject *parent)
    : QObject(parent)
{
}

UsbDevice::UsbDevice(const QString &vid, const QString &pid, const QString &devicePath,
                     const QString &manufacturer, const QString &product,
                     const QString &deviceClass, const QString &status,
                     QObject *parent)
    : QObject(parent)
    , m_vid(vid)
    , m_pid(pid)
    , m_devicePath(devicePath)
    , m_manufacturer(manufacturer)
    , m_product(product)
    , m_deviceClass(deviceClass)
    , m_status(status)
{
}

void UsbDevice::setVid(const QString &vid)
{
    if (m_vid != vid) {
        m_vid = vid;
        emit vidChanged();
    }
}

void UsbDevice::setPid(const QString &pid)
{
    if (m_pid != pid) {
        m_pid = pid;
        emit pidChanged();
    }
}

void UsbDevice::setDevicePath(const QString &devicePath)
{
    if (m_devicePath != devicePath) {
        m_devicePath = devicePath;
        emit devicePathChanged();
    }
}

void UsbDevice::setManufacturer(const QString &manufacturer)
{
    if (m_manufacturer != manufacturer) {
        m_manufacturer = manufacturer;
        emit manufacturerChanged();
    }
}

void UsbDevice::setProduct(const QString &product)
{
    if (m_product != product) {
        m_product = product;
        emit productChanged();
    }
}

void UsbDevice::setDeviceClass(const QString &deviceClass)
{
    if (m_deviceClass != deviceClass) {
        m_deviceClass = deviceClass;
        emit deviceClassChanged();
    }
}

void UsbDevice::setStatus(const QString &status)
{
    if (m_status != status) {
        m_status = status;
        emit statusChanged();
    }
}
