#include <QGuiApplication>
#include <QQmlApplicationEngine>
#include <QQmlContext>
#include <QtQml>
#include "usbmonitor.h"
#include "usbdevice.h"

int main(int argc, char *argv[])
{
    QGuiApplication app(argc, argv);
    
    // Register QML types
    qmlRegisterType<UsbMonitor>("UsbDetect", 1, 0, "UsbMonitor");
    qmlRegisterType<UsbDevice>("UsbDetect", 1, 0, "UsbDevice");
    
    QQmlApplicationEngine engine;
    
    // Create USB monitor instance
    UsbMonitor usbMonitor;
    engine.rootContext()->setContextProperty("usbMonitor", &usbMonitor);
    
    const QUrl url(QStringLiteral("qrc:/UsbDetect/main.qml"));
    QObject::connect(&engine, &QQmlApplicationEngine::objectCreated,
                     &app, [url](QObject *obj, const QUrl &objUrl) {
        if (!obj && url == objUrl)
            QCoreApplication::exit(-1);
    }, Qt::QueuedConnection);
    
    engine.load(url);
    
    return app.exec();
}
