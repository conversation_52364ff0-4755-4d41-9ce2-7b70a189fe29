QMAKE_CXX.QT_COMPILER_STDCXX = 201703L
QMAKE_CXX.QMAKE_GCC_MAJOR_VERSION = 13
QMAKE_CXX.QMAKE_GCC_MINOR_VERSION = 2
QMAKE_CXX.QMAKE_GCC_PATCH_VERSION = 0
QMAKE_CXX.COMPILER_MACROS = \
    QT_COMPILER_STDCXX \
    QMAKE_GCC_MAJOR_VERSION \
    QMAKE_GCC_MINOR_VERSION \
    QMAKE_GCC_PATCH_VERSION
QMAKE_CXX.INCDIRS = \
    C:/msys64/mingw64/include/c++/13.2.0 \
    C:/msys64/mingw64/include/c++/13.2.0/x86_64-w64-mingw32 \
    C:/msys64/mingw64/include/c++/13.2.0/backward \
    C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/13.2.0/include \
    C:/msys64/mingw64/include \
    C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/13.2.0/include-fixed
QMAKE_CXX.LIBDIRS = \
    C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/13.2.0 \
    C:/msys64/mingw64/lib/gcc \
    C:/msys64/mingw64/x86_64-w64-mingw32/lib \
    C:/msys64/mingw64/lib
