// 简单的测试程序，用于验证USB监控功能
// 编译: g++ -std=c++17 test_basic.cpp -o test_basic

#include <iostream>
#include <string>

#ifdef _WIN32
#include <windows.h>
#include <setupapi.h>
#include <devguid.h>
#pragma comment(lib, "setupapi.lib")
#elif defined(__linux__)
#include <libudev.h>
#endif

void testWindowsUsbDetection() {
#ifdef _WIN32
    std::cout << "Testing Windows USB detection..." << std::endl;
    
    HDEVINFO deviceInfoSet = SetupDiGetClassDevs(&GUID_DEVINTERFACE_USB_DEVICE, 
                                                  nullptr, nullptr, 
                                                  DIGCF_PRESENT | DIGCF_DEVICEINTERFACE);
    
    if (deviceInfoSet == INVALID_HANDLE_VALUE) {
        std::cout << "Failed to get device info set" << std::endl;
        return;
    }

    SP_DEVINFO_DATA deviceInfoData;
    deviceInfoData.cbSize = sizeof(SP_DEVINFO_DATA);
    
    int deviceCount = 0;
    for (DWORD i = 0; SetupDiEnumDeviceInfo(deviceInfoSet, i, &deviceInfoData); i++) {
        deviceCount++;
    }
    
    std::cout << "Found " << deviceCount << " USB devices" << std::endl;
    SetupDiDestroyDeviceInfoList(deviceInfoSet);
#else
    std::cout << "Windows USB detection not available on this platform" << std::endl;
#endif
}

void testLinuxUsbDetection() {
#ifdef __linux__
    std::cout << "Testing Linux USB detection..." << std::endl;
    
    struct udev *udev = udev_new();
    if (!udev) {
        std::cout << "Cannot create udev context" << std::endl;
        return;
    }
    
    struct udev_enumerate *enumerate = udev_enumerate_new(udev);
    if (!enumerate) {
        std::cout << "Cannot create udev enumerate" << std::endl;
        udev_unref(udev);
        return;
    }
    
    udev_enumerate_add_match_subsystem(enumerate, "usb");
    udev_enumerate_add_match_property(enumerate, "DEVTYPE", "usb_device");
    udev_enumerate_scan_devices(enumerate);
    
    struct udev_list_entry *devices = udev_enumerate_get_list_entry(enumerate);
    struct udev_list_entry *entry;
    
    int deviceCount = 0;
    udev_list_entry_foreach(entry, devices) {
        deviceCount++;
    }
    
    std::cout << "Found " << deviceCount << " USB devices" << std::endl;
    
    udev_enumerate_unref(enumerate);
    udev_unref(udev);
#else
    std::cout << "Linux USB detection not available on this platform" << std::endl;
#endif
}

int main() {
    std::cout << "USB Device Detection Test" << std::endl;
    std::cout << "=========================" << std::endl;
    
#ifdef _WIN32
    testWindowsUsbDetection();
#elif defined(__linux__)
    testLinuxUsbDetection();
#else
    std::cout << "Platform not supported for USB detection" << std::endl;
#endif
    
    return 0;
}
