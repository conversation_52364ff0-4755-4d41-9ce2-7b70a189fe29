cmake_minimum_required(VERSION 3.16)

project(UsbDetect VERSION 1.0.0 LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

find_package(Qt6 6.8 REQUIRED COMPONENTS Core Quick)

qt_standard_project_setup()

qt_add_executable(UsbDetect
    main.cpp
    usbmonitor.cpp
    usbmonitor.h
    usbdevice.cpp
    usbdevice.h
)

qt_add_qml_module(UsbDetect
    URI UsbDetect
    VERSION 1.0
    QML_FILES
        main.qml
        UsbDeviceDelegate.qml
)

target_link_libraries(UsbDetect
    Qt6::Core
    Qt6::Quick
)

# Platform-specific libraries
if(WIN32)
    target_link_libraries(UsbDetect setupapi)
elseif(UNIX AND NOT APPLE)
    find_package(PkgConfig REQUIRED)
    pkg_check_modules(UDEV REQUIRED libudev)
    target_link_libraries(UsbDetect ${UDEV_LIBRARIES})
    target_include_directories(UsbDetect PRIVATE ${UDEV_INCLUDE_DIRS})
endif()

# Set the startup project for Visual Studio
set_property(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR} PROPERTY VS_STARTUP_PROJECT UsbDetect)
