#ifndef USBDEVICE_H
#define USBDEVICE_H

#include <QObject>
#include <QString>

class UsbDevice : public QObject
{
    Q_OBJECT
    Q_PROPERTY(QString vid READ vid NOTIFY vidChanged)
    Q_PROPERTY(QString pid READ pid NOTIFY pidChanged)
    Q_PROPERTY(QString devicePath READ devicePath NOTIFY devicePathChanged)
    Q_PROPERTY(QString manufacturer READ manufacturer NOTIFY manufacturerChanged)
    Q_PROPERTY(QString product READ product NOTIFY productChanged)
    Q_PROPERTY(QString deviceClass READ deviceClass NOTIFY deviceClassChanged)
    Q_PROPERTY(QString status READ status NOTIFY statusChanged)

public:
    explicit UsbDevice(QObject *parent = nullptr);
    UsbDevice(const QString &vid, const QString &pid, const QString &devicePath,
              const QString &manufacturer, const QString &product, 
              const QString &deviceClass, const QString &status,
              QObject *parent = nullptr);

    QString vid() const { return m_vid; }
    QString pid() const { return m_pid; }
    QString devicePath() const { return m_devicePath; }
    QString manufacturer() const { return m_manufacturer; }
    QString product() const { return m_product; }
    QString deviceClass() const { return m_deviceClass; }
    QString status() const { return m_status; }

    void setVid(const QString &vid);
    void setPid(const QString &pid);
    void setDevicePath(const QString &devicePath);
    void setManufacturer(const QString &manufacturer);
    void setProduct(const QString &product);
    void setDeviceClass(const QString &deviceClass);
    void setStatus(const QString &status);

signals:
    void vidChanged();
    void pidChanged();
    void devicePathChanged();
    void manufacturerChanged();
    void productChanged();
    void deviceClassChanged();
    void statusChanged();

private:
    QString m_vid;
    QString m_pid;
    QString m_devicePath;
    QString m_manufacturer;
    QString m_product;
    QString m_deviceClass;
    QString m_status;
};

#endif // USBDEVICE_H
